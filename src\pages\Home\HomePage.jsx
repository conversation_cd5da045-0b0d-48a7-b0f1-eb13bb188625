import React, { useEffect, useState } from "react";
import Layout from "../../components/layout/Layout";
import useUserStore from "../../store/userStore";
import useCraftsmenStore from "../../store/craftsmenStore";
import useBookingStore from "../../store/bookingStore";
import useThemeStore from "../../store/themeStore";
import LoginRedirect from "../../components/auth/LoginRedirect";
import BookingDetailsModal from "../../components/bookings/BookingDetailsModal";
import bookingService from "../../services/bookingService";

// Importar componentes
import WelcomeBanner from "./components/WelcomeBanner";
import QuickActions from "./components/QuickActions";
import AvailableProfessions from "./components/AvailableProfessions";
import RecentBookings from "./components/RecentBookings";
import RecommendedCraftsmen from "./components/RecommendedCraftsmen";

const HomePage = () => {
  const user = useUserStore((state) => state.user);
  const userType = useUserStore((state) => state.userType);
  const logout = useUserStore((state) => state.logout);
  const darkMode = useThemeStore((state) => state.darkMode);
  const isAuthenticated = useUserStore((state) => state.isAuthenticated);

  const { craftsmen, fetchCraftsmen } = useCraftsmenStore();
  const { getUserBookings } = useBookingStore();

  const [userBookings, setUserBookings] = useState([]);
  const [recommendedCraftsmen, setRecommendedCraftsmen] = useState([]);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [isLoadingBookings, setIsLoadingBookings] = useState(true);
  const [isLoadingCraftsmen, setIsLoadingCraftsmen] = useState(true);
  const [authChecked, setAuthChecked] = useState(false);
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  // Función para formatear la hora en formato de 12 horas con indicador AM/PM en árabe
  const formatTime = (timeString) => {
    // Convertir el formato de 24 horas a formato de 12 horas con indicador AM/PM en árabe
    const [hours, minutes] = timeString.split(":");
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? "م" : "ص"; // م para PM, ص para AM
    const hour12 = hour % 12 || 12; // Convertir 0 a 12
    return `${hour12}:${minutes} ${ampm}`;
  };

  useEffect(() => {
    // Fetch craftsmen once when component mounts
    const loadCraftsmen = async () => {
      try {
        setIsLoadingCraftsmen(true);
        await fetchCraftsmen();
      } catch (error) {
        console.error("خطأ في تحميل الحرفيين:", error);
      } finally {
        setIsLoadingCraftsmen(false);
      }
    };

    loadCraftsmen();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Remove dependencies to prevent multiple calls

  // التحقق الذكي من الجلسة - فقط للزيارات المباشرة
  useEffect(() => {
    const verifyAuth = async () => {
      // التحقق من مصدر الزيارة
      const isDirectAccess =
        !document.referrer ||
        !document.referrer.includes(window.location.origin) ||
        performance.getEntriesByType("navigation")[0]?.type === "reload";

      // إذا كان المستخدم موجودًا بالفعل، فلا داعي للتحقق
      if (user) {
        console.log("المستخدم موجود بالفعل، تخطي عملية التحقق");
        setAuthChecked(true);
        setIsCheckingAuth(false);
        return;
      }

      // إذا لم يكن وصول مباشر، تخطي التحقق التلقائي
      if (!isDirectAccess) {
        console.log("تنقل داخلي، تخطي التحقق التلقائي من الجلسة");
        setAuthChecked(true);
        setIsCheckingAuth(false);
        return;
      }

      // فقط للوصول المباشر - التحقق من وجود جلسة سابقة
      const token = localStorage.getItem("token");
      const userData = localStorage.getItem("user");
      const tokenExpiry = localStorage.getItem("tokenExpiry");

      if (token && userData) {
        // التحقق من انتهاء صلاحية التوكن
        if (tokenExpiry) {
          const expiryDate = new Date(tokenExpiry);
          const now = new Date();

          if (now > expiryDate) {
            // انتهت الصلاحية، مسح البيانات
            localStorage.removeItem("token");
            localStorage.removeItem("user");
            localStorage.removeItem("tokenExpiry");
            setAuthChecked(true);
            setIsCheckingAuth(false);
            return;
          }
        }

        try {
          const userObj = JSON.parse(userData);
          console.log("جلسة سابقة صالحة موجودة، تحديث حالة المستخدم");

          // تحديث حالة المستخدم في المتجر
          const userStore = useUserStore.getState();
          userStore.login(userObj, userObj.userType, true);

          setAuthChecked(true);
          setIsCheckingAuth(false);
          return;
        } catch (error) {
          console.error("خطأ في تحليل بيانات المستخدم:", error);
          // مسح البيانات التالفة
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          localStorage.removeItem("tokenExpiry");
        }
      }

      // لا توجد جلسة صالحة
      setAuthChecked(true);
      setIsCheckingAuth(false);
    };

    verifyAuth();
  }, [user]);

  useEffect(() => {
    // Solo cargar datos si el usuario está autenticado y la verificación ha terminado
    if (user && !isCheckingAuth) {
      // Fetch bookings from server first
      const loadBookings = async () => {
        try {
          // بدء التحميل
          setIsLoadingBookings(true);

          // جلب الحجوزات من الخادم
          await useBookingStore.getState().fetchBookings();
          console.log("تم تحميل الحجوزات من الخادم بنجاح");

          // الحصول على معرف المستخدم
          const userId = user._id || user.id;
          console.log("جلب الحجوزات للمستخدم:", { userId, userType });

          // إضافة تأخير قبل جلب الحجوزات للتأكد من تحميل البيانات بشكل كامل
          await new Promise((resolve) => setTimeout(resolve, 500));

          // جلب حجوزات المستخدم
          const bookings = getUserBookings(userId, userType);
          console.log("الحجوزات التي تم جلبها:", bookings);

          // تم إزالة وظيفة التحقق من الطلبات المنتهية وإلغائها تلقائيًا

          // تحديث حالة الحجوزات مباشرة
          setUserBookings(bookings);

          // إضافة تأخير قبل إخفاء مؤشر التحميل
          setTimeout(() => {
            setIsLoadingBookings(false);
          }, 1500); // زيادة التأخير لضمان ظهور البيانات
        } catch (error) {
          console.error("خطأ في تحميل الحجوزات:", error);
          // في حالة حدوث خطأ
          setTimeout(() => {
            setIsLoadingBookings(false);
          }, 1000);
        }
      };

      loadBookings();

      // Set recommended craftsmen when craftsmen data changes
      if (craftsmen.length > 0) {
        // Ordenar por rating (de mayor a menor) y tomar los 3 primeros
        const sortedCraftsmen = [...craftsmen].sort(
          (a, b) => (b.rating || 0) - (a.rating || 0)
        );
        setRecommendedCraftsmen(sortedCraftsmen.slice(0, 3));
      }
    }
  }, [user, userType, craftsmen, getUserBookings, isCheckingAuth]);

  // تخطي عرض مؤشر التحميل إذا كان المستخدم مسجل الدخول بالفعل
  // وإظهاره فقط إذا كان هناك تحقق فعلي من المصادقة
  if (isCheckingAuth && !user) {
    return (
      <div
        className={`min-h-screen flex items-center justify-center ${
          darkMode
            ? "bg-gradient-to-b from-gray-900 via-gray-900 to-indigo-950/80"
            : "bg-gradient-to-b from-white via-indigo-50/40 to-blue-50/60"
        }`}
      >
        <div
          className={`text-center p-8 rounded-xl ${
            darkMode
              ? "bg-gray-800/50 border border-gray-700"
              : "bg-white/70 border border-indigo-100/60"
          } backdrop-blur-sm shadow-xl max-w-md`}
        >
          {/* Spinner animado mejorado */}
          <div className="relative w-20 h-20 mx-auto mb-6">
            {/* Círculo exterior */}
            <div
              className={`absolute inset-0 rounded-full ${
                darkMode
                  ? "border-2 border-indigo-700"
                  : "border-2 border-indigo-200"
              }`}
            ></div>

            {/* Spinner animado */}
            <div
              className={`absolute inset-0 rounded-full border-2 border-t-transparent border-l-transparent ${
                darkMode
                  ? "border-r-indigo-400 border-b-indigo-500"
                  : "border-r-indigo-500 border-b-indigo-600"
              } animate-spin`}
            ></div>

            {/* Punto central */}
            <div
              className={`absolute inset-0 m-auto w-2 h-2 rounded-full ${
                darkMode ? "bg-indigo-400" : "bg-indigo-500"
              }`}
            ></div>
          </div>

          <h3
            className={`text-xl font-bold mb-2 ${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            }`}
          >
            جاري التحقق من حالة تسجيل الدخول
          </h3>

          <p
            className={`${
              darkMode ? "text-gray-400" : "text-gray-600"
            } transition-colors duration-300`}
          >
            يرجى الانتظار بينما نتحقق من حسابك...
          </p>

          {/* Puntos de carga animados */}
          <div className="flex justify-center mt-4 space-x-2">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={`w-2 h-2 rounded-full ${
                  darkMode ? "bg-indigo-500" : "bg-indigo-600"
                } opacity-0 animate-pulse`}
                style={{
                  animationDelay: `${i * 0.3}s`,
                  animationDuration: "1.5s",
                }}
              ></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Mostrar LoginRedirect solo si se ha completado la verificación y no hay usuario
  if (authChecked && !user) {
    return <LoginRedirect />;
  }

  return (
    <Layout user={user} onLogout={logout}>
      <div
        className={`min-h-screen ${
          darkMode
            ? "bg-gradient-to-b from-gray-900 via-gray-900 to-indigo-950/80"
            : "bg-gradient-to-b from-white via-indigo-50/40 to-blue-50/60"
        } transition-colors duration-300`}
      >
        {/* Welcome Banner */}
        <WelcomeBanner user={user} userType={userType} />

        <div className="container mx-auto px-4 py-8">
          {/* Quick Actions */}
          <QuickActions />

          {/* Available Professions */}
          <AvailableProfessions />

          {/* Recent Bookings */}
          <RecentBookings
            isLoadingBookings={isLoadingBookings}
            userBookings={userBookings}
            userType={userType}
            darkMode={darkMode}
            formatTime={formatTime}
            onViewDetails={(booking) => {
              setSelectedBooking(booking);
              setShowDetailsModal(true);
            }}
          />

          {/* Recommended Craftsmen */}
          <RecommendedCraftsmen
            isLoadingCraftsmen={isLoadingCraftsmen}
            recommendedCraftsmen={recommendedCraftsmen}
            userType={userType}
          />
        </div>
      </div>

      {/* Modal de detalles del pedido */}
      {showDetailsModal && selectedBooking && (
        <BookingDetailsModal
          booking={selectedBooking}
          onClose={async () => {
            setShowDetailsModal(false);
            setSelectedBooking(null);
            // Actualizar la lista de pedidos después de cerrar el modal
            if (user) {
              try {
                // Fetch bookings from server first
                await useBookingStore.getState().fetchBookings();
                const userId = user._id || user.id;
                const bookings = getUserBookings(userId, userType);
                setUserBookings(bookings);

                // تحديث الإشعارات بعد تحديث الطلبات
                try {
                  const { default: useNotificationStore } = await import(
                    "../../store/notificationStore"
                  );
                  useNotificationStore.getState().fetchNotifications();
                } catch (error) {
                  console.log("تعذر تحديث الإشعارات:", error);
                }
              } catch (error) {
                console.error("خطأ في تحديث الحجوزات:", error);
              }
            }
          }}
          userType={userType}
        />
      )}
    </Layout>
  );
};

export default HomePage;
