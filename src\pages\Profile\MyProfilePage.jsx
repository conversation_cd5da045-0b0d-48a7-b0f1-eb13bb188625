import React, { useEffect, useState } from "react";
import { motion } from "framer-motion";
import Layout from "../../components/layout/Layout";
import Button from "../../components/common/Button";
import LoadingButton from "../../components/common/LoadingButton";
import Input from "../../components/common/Input";
import SimpleLazyImage from "../../components/common/SimpleLazyImage";
import ProfessionSelector from "../../components/common/ProfessionSelector";
import useUserStore from "../../store/userStore";
import useThemeStore from "../../store/themeStore";
import { Link } from "react-router-dom";
import LoginRedirect from "../../components/auth/LoginRedirect";

// Import profile components
import ProfileHeader from "./components/ProfileHeader";
import ProfileInfo from "./components/ProfileInfo";
import ProfileBio from "./components/ProfileBio";
import ProfileGallery from "./components/ProfileGallery";
import { WorkGalleryManager } from "../../components/WorkGallery";
import ProfileLocation from "./components/ProfileLocation";
import ProfileWorkingHours from "./components/ProfileWorkingHours";

// تعريف تأثيرات النبض للحالة
const pulseStyles = `
@keyframes pulse-green {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.7);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
  }
  70% {
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
  }
}
`;

const MyProfilePage = () => {
  // const navigate = useNavigate(); // Used in handleSaveChanges
  const user = useUserStore((state) => state.user);
  const userType = useUserStore((state) => state.userType);
  const updateUser = useUserStore((state) => state.updateUser);
  const logout = useUserStore((state) => state.logout);
  const darkMode = useThemeStore((state) => state.darkMode);

  const [isEditing, setIsEditing] = useState(false);
  const [editedUser, setEditedUser] = useState(null);
  const [workingHoursTime, setWorkingHoursTime] = useState({
    start: "",
    end: "",
  });
  const [isSaving, setIsSaving] = useState(false);
  const [errors, setErrors] = useState({});

  // إضافة تأثيرات النبض إلى الصفحة
  useEffect(() => {
    const styleElement = document.createElement("style");
    styleElement.textContent = pulseStyles;
    document.head.appendChild(styleElement);

    return () => {
      document.head.removeChild(styleElement);
    };
  }, []);

  useEffect(() => {
    // إضافة متغير للتحكم في تنفيذ الوظيفة لمنع الحلقات اللانهائية
    let isMounted = true;

    // مسح المتغيرات العامة عند تغيير المستخدم لضمان إعادة التحميل
    const userId = user?.id || user?._id;
    if (userId) {
      // مسح جميع المتغيرات المتعلقة بالمستخدم السابق
      Object.keys(window).forEach((key) => {
        if (
          key.startsWith("galleryLoadAttempted_") ||
          key.startsWith("professionsLoadAttempted_")
        ) {
          delete window[key];
        }
      });
    }

    const loadUserData = async () => {
      if (user && isMounted) {
        // تحويل البيانات القديمة إلى التنسيق الجديد إذا لزم الأمر
        const updatedUser = { ...user };

        // التأكد من أن professions موجود كمصفوفة
        if (userType === "craftsman") {
          // التأكد من أن professions موجود كمصفوفة
          if (
            !updatedUser.professions ||
            !Array.isArray(updatedUser.professions)
          ) {
            // إذا كان هناك profession قديم، نضيفه إلى المصفوفة
            if (updatedUser.profession) {
              updatedUser.professions = [updatedUser.profession];
            } else {
              updatedUser.professions = [];
            }
          }

          // التأكد من أن specializations موجود كمصفوفة
          if (
            !updatedUser.specializations ||
            !Array.isArray(updatedUser.specializations)
          ) {
            // إذا كان هناك specialization قديم، نضيفه إلى المصفوفة
            if (updatedUser.specialization) {
              updatedUser.specializations = [updatedUser.specialization];
            } else {
              updatedUser.specializations = [];
            }
          }

          // إذا كانت المهن أو التخصصات فارغة، حاول جلبها من الخادم
          // إنشاء مفتاح فريد لكل جلسة لتجنب التحميل المتكرر في نفس الجلسة
          const professionSessionKey = `professionsLoadAttempted_${user.id ||
            user._id}_${Date.now()}`;

          if (
            (!updatedUser.professions ||
              updatedUser.professions.length === 0 ||
              !updatedUser.specializations ||
              updatedUser.specializations.length === 0) &&
            !window[professionSessionKey]
          ) {
            window[professionSessionKey] = true;
            try {
              console.log("محاولة جلب بيانات الحرفي من الخادم...");
              const { craftsmanService } = await import("../../services/api");
              const craftsmanProfile = await craftsmanService.getMyProfile();
              console.log("تم جلب بيانات الحرفي من الخادم:", craftsmanProfile);

              // تحديث المهن والتخصصات إذا كانت موجودة في البيانات المجلوبة
              if (craftsmanProfile && isMounted) {
                // متغير لتتبع التغييرات

                if (
                  craftsmanProfile.professions &&
                  craftsmanProfile.professions.length > 0
                ) {
                  updatedUser.professions = craftsmanProfile.professions;
                  // تم تحديث البيانات
                  console.log(
                    "تم تحديث المهن من الخادم:",
                    updatedUser.professions
                  );
                }

                if (
                  craftsmanProfile.specializations &&
                  craftsmanProfile.specializations.length > 0
                ) {
                  updatedUser.specializations =
                    craftsmanProfile.specializations;
                  // تم تحديث البيانات
                  console.log(
                    "تم تحديث التخصصات من الخادم:",
                    updatedUser.specializations
                  );
                }

                // تحديث ساعات العمل إذا كانت موجودة
                if (craftsmanProfile.workingHours) {
                  // تحويل صيغة ساعات العمل من الباك إند إلى الفرونت إند
                  const convertedWorkingHours = {};

                  console.log(
                    "ساعات العمل المستلمة من الباك إند في loadUserData:",
                    JSON.stringify(craftsmanProfile.workingHours)
                  );

                  Object.entries(craftsmanProfile.workingHours).forEach(
                    ([day, hours]) => {
                      if (hours) {
                        // استخدام start/end مباشرة بدلاً من from/to
                        convertedWorkingHours[day] = {
                          start: hours.start || "",
                          end: hours.end || "",
                          // استخدام == بدلاً من === للتعامل مع القيم النصية والمنطقية
                          isWorking: hours.isWorking == true ? true : false,
                        };
                      }
                    }
                  );

                  updatedUser.workingHours = convertedWorkingHours;
                  // تم تحديث البيانات
                  console.log(
                    "تم تحديث ساعات العمل من الخادم:",
                    updatedUser.workingHours
                  );
                }

                // تحديث الخصائص إذا كانت موجودة في البيانات المجلوبة
                if (craftsmanProfile.features) {
                  if (Array.isArray(craftsmanProfile.features)) {
                    updatedUser.features = craftsmanProfile.features;
                  } else {
                    // إذا لم تكن مصفوفة، نحولها إلى مصفوفة
                    updatedUser.features = [craftsmanProfile.features];
                  }
                  // تم تحديث البيانات
                  console.log(
                    "تم تحديث الخصائص من الخادم:",
                    updatedUser.features,
                    "النوع:",
                    typeof updatedUser.features,
                    "هل هي مصفوفة:",
                    Array.isArray(updatedUser.features),
                    "الطول:",
                    updatedUser.features.length
                  );
                } else {
                  // إذا لم تكن موجودة، نستخدم مصفوفة فارغة
                  updatedUser.features = [];
                  console.log(
                    "الخصائص غير موجودة في البيانات المجلوبة، استخدام مصفوفة فارغة"
                  );
                }
              }
            } catch (error) {
              console.error("خطأ في جلب بيانات الحرفي من الخادم:", error);
            }
          }

          // جلب معرض الأعمال من الخادم إذا كان المستخدم حرفي
          try {
            // استخدام البيانات المخزنة في المتجر إذا كانت متوفرة
            if (user.workGallery && user.workGallery.length > 0) {
              updatedUser.gallery = user.workGallery;
              console.log(
                "استخدام معرض الأعمال من workGallery:",
                user.workGallery.length
              );
            } else if (user.gallery && user.gallery.length > 0) {
              updatedUser.gallery = user.gallery;
              console.log(
                "استخدام معرض الأعمال من gallery:",
                user.gallery.length
              );
            } else {
              // استخدام مصفوفة فارغة مؤقتًا
              updatedUser.gallery = [];

              // إنشاء مفتاح فريد لكل جلسة لتجنب التحميل المتكرر في نفس الجلسة
              const sessionKey = `galleryLoadAttempted_${user.id ||
                user._id}_${Date.now()}`;

              // تحميل المعرض إذا لم يتم تحميله في هذه الجلسة
              if (!window[sessionKey]) {
                window[sessionKey] = true;

                console.log("بدء تحميل معرض الأعمال من الخادم...");

                // تأخير طلب المعرض لتجنب الحلقات اللانهائية
                setTimeout(async () => {
                  if (isMounted) {
                    try {
                      const { craftsmanService } = await import(
                        "../../services/api"
                      );
                      const craftsmanId = user.id || user._id;
                      console.log("طلب معرض الأعمال للحرفي:", craftsmanId);

                      const galleryData = await craftsmanService.getCraftsmanGallery(
                        craftsmanId
                      );

                      console.log("استجابة معرض الأعمال:", galleryData);

                      // تحديث gallery في بيانات المستخدم
                      if (
                        galleryData &&
                        (galleryData.gallery || galleryData.workGallery)
                      ) {
                        const newGallery =
                          galleryData.gallery || galleryData.workGallery || [];

                        console.log(
                          "تحديث معرض الأعمال بـ",
                          newGallery.length,
                          "صورة"
                        );

                        // تحديث المستخدم مباشرة بدون إعادة تحميل البيانات
                        setEditedUser((prev) => ({
                          ...prev,
                          gallery: newGallery,
                        }));

                        // تحديث المستخدم في المتجر أيضاً
                        updateUser({
                          ...user,
                          gallery: newGallery,
                          workGallery: newGallery,
                          skipApiCall: true,
                        });
                      } else {
                        console.log("لا توجد صور في معرض الأعمال");
                      }
                    } catch (error) {
                      console.error("خطأ في تحميل معرض الأعمال:", error);
                    }
                  }
                }, 1000); // تقليل التأخير إلى ثانية واحدة
              }
            }
          } catch (error) {
            console.error("خطأ في معالجة معرض الأعمال:", error);
            updatedUser.gallery = [];
          }
        }

        // تحديث حالة المستخدم المحلية فقط إذا كان المكون لا يزال مثبتًا
        if (isMounted) {
          setEditedUser(updatedUser);
        }

        // تجنب تحديث المتجر إذا كانت البيانات متطابقة لمنع الحلقات اللانهائية
        const professionsDifferent =
          JSON.stringify(user.professions) !==
          JSON.stringify(updatedUser.professions || []);
        const specializationsDifferent =
          JSON.stringify(user.specializations) !==
          JSON.stringify(updatedUser.specializations || []);

        const shouldUpdateStore =
          userType === "craftsman" &&
          ((updatedUser.professions && updatedUser.professions.length > 0) ||
            (updatedUser.specializations &&
              updatedUser.specializations.length > 0)) &&
          (professionsDifferent || specializationsDifferent);

        if (shouldUpdateStore && isMounted) {
          // تحديث المتجر مع علامة skipApiCall لتجنب الطلبات المتكررة
          updateUser({
            ...user,
            professions: updatedUser.professions || [],
            specializations: updatedUser.specializations || [],
            features: updatedUser.features || [],
            workingHours: updatedUser.workingHours || {}, // إضافة ساعات العمل
            skipApiCall: true, // تجنب استدعاء API مرة أخرى
          });
        }
      }
    };

    loadUserData();

    // تنظيف عند تفكيك المكون
    return () => {
      isMounted = false;
    };
  }, [user, userType]);

  // استخدام حالة تحميل بدلاً من إعادة التوجيه إلى صفحة تسجيل الدخول
  const [isLoading, setIsLoading] = useState(true);

  // إضافة متغيرات لتخزين بيانات الأماكن
  const [streetsInWorkRange, setStreetsInWorkRange] = useState([]);
  const [hospitalsInWorkRange, setHospitalsInWorkRange] = useState([]);
  const [mosquesInWorkRange, setMosquesInWorkRange] = useState([]);

  // إضافة مستمع لحدث تحديث بيانات الأماكن
  useEffect(() => {
    const handlePlacesDataUpdated = (event) => {
      if (isEditing && editedUser) {
        const {
          streetsInWorkRange: newStreets,
          hospitalsInWorkRange: newHospitals,
          mosquesInWorkRange: newMosques,
        } = event.detail;

        console.log("تم استلام بيانات الأماكن المحدثة:", {
          streetsInWorkRange: newStreets,
          hospitalsInWorkRange: newHospitals,
          mosquesInWorkRange: newMosques,
        });

        // تحديث متغيرات الأماكن
        setStreetsInWorkRange(newStreets || []);
        setHospitalsInWorkRange(newHospitals || []);
        setMosquesInWorkRange(newMosques || []);

        // تحديث بيانات المستخدم المحلية
        setEditedUser((prevState) => ({
          ...prevState,
          streetsInWorkRange: newStreets || [],
          hospitalsInWorkRange: newHospitals || [],
          mosquesInWorkRange: newMosques || [],
        }));
      }
    };

    // مستمع لحدث تغيير الموقع
    const handleLocationChanged = (event) => {
      if (isEditing && editedUser && userType === "craftsman") {
        const { location } = event.detail;

        console.log("تم استلام موقع جديد:", location);

        // مسح التخزين المؤقت للخريطة
        import("../../services/mapService").then((module) => {
          const { clearMapCache } = module;
          clearMapCache();
        });

        // تحديث الموقع في حالة المستخدم المحلية
        setEditedUser({
          ...editedUser,
          location,
        });

        // تحديث الموقع في الخادم
        (async () => {
          try {
            const { craftsmanService } = await import("../../services/api");

            // إعداد بيانات الحرفي للتحديث
            const craftsmanData = {
              professions: editedUser.professions || [],
              specializations: editedUser.specializations || [],
              bio: editedUser.bio || "",
              features: editedUser.features || [],
              workRadius: editedUser.workRadius || 5,
              location: location, // استخدام الموقع الجديد
              workingHours: editedUser.workingHours || {},
              // إضافة الشوارع والمساجد والمستشفيات المحدثة
              streetsInWorkRange: streetsInWorkRange || [],
              hospitalsInWorkRange: hospitalsInWorkRange || [],
              mosquesInWorkRange: mosquesInWorkRange || [],
            };

            console.log("تحديث بيانات الموقع في الخادم:", craftsmanData);

            // استدعاء API لتحديث بيانات الحرفي
            const craftsmanResult = await craftsmanService.updateProfile(
              craftsmanData
            );

            console.log("استجابة تحديث بيانات الموقع:", craftsmanResult);
          } catch (error) {
            console.error("خطأ في تحديث بيانات الموقع مباشرة:", error);
          }
        })();
      }
    };

    // مستمع لحدث تغيير نطاق العمل
    const handleRadiusChanged = (event) => {
      if (isEditing && editedUser && userType === "craftsman") {
        const { workRadius } = event.detail;

        console.log("تم استلام نطاق عمل جديد:", workRadius);

        // مسح التخزين المؤقت للخريطة
        import("../../services/mapService").then((module) => {
          const { clearMapCache } = module;
          clearMapCache();
        });

        // تحديث نطاق العمل في حالة المستخدم المحلية
        setEditedUser({
          ...editedUser,
          workRadius,
        });

        // تحديث نطاق العمل في الخادم
        (async () => {
          try {
            const { craftsmanService } = await import("../../services/api");

            // إعداد بيانات الحرفي للتحديث
            const craftsmanData = {
              professions: editedUser.professions || [],
              specializations: editedUser.specializations || [],
              bio: editedUser.bio || "",
              features: editedUser.features || [],
              workRadius: workRadius, // استخدام نطاق العمل الجديد
              location: editedUser.location || {},
              workingHours: editedUser.workingHours || {},
              // إضافة الشوارع والمساجد والمستشفيات المحدثة
              streetsInWorkRange: streetsInWorkRange || [],
              hospitalsInWorkRange: hospitalsInWorkRange || [],
              mosquesInWorkRange: mosquesInWorkRange || [],
            };

            console.log("تحديث بيانات نطاق العمل في الخادم:", craftsmanData);

            // استدعاء API لتحديث بيانات الحرفي
            const craftsmanResult = await craftsmanService.updateProfile(
              craftsmanData
            );

            console.log("استجابة تحديث بيانات نطاق العمل:", craftsmanResult);
          } catch (error) {
            console.error("خطأ في تحديث بيانات نطاق العمل مباشرة:", error);
          }
        })();
      }
    };

    // إضافة مستمعي الأحداث
    window.addEventListener("placesDataUpdated", handlePlacesDataUpdated);
    window.addEventListener("locationChanged", handleLocationChanged);
    window.addEventListener("radiusChanged", handleRadiusChanged);

    // إزالة مستمعي الأحداث عند تفكيك المكون
    return () => {
      window.removeEventListener("placesDataUpdated", handlePlacesDataUpdated);
      window.removeEventListener("locationChanged", handleLocationChanged);
      window.removeEventListener("radiusChanged", handleRadiusChanged);
    };
  }, [isEditing, editedUser, userType]);

  useEffect(() => {
    // تعيين حالة التحميل بناءً على توفر بيانات المستخدم
    if (user && editedUser) {
      // تأخير قصير لضمان تحميل جميع البيانات
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 300);

      return () => clearTimeout(timer);
    } else {
      setIsLoading(true);
    }
  }, [user, editedUser]);

  // إذا كان المستخدم غير مسجل دخول بعد انتهاء التحميل، قم بإعادة التوجيه
  if (!user && !isLoading) {
    return <LoginRedirect />;
  }

  // عرض شاشة التحميل أثناء التحقق من المصادقة وتحميل البيانات
  if (isLoading) {
    return (
      <Layout>
        <div
          className={`min-h-screen ${
            darkMode
              ? "bg-gray-900"
              : "bg-gradient-to-br from-blue-50 to-indigo-100"
          } transition-colors duration-300`}
        >
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col items-center justify-center py-12">
              <div className="relative mb-6">
                <div
                  className={`w-16 h-16 rounded-full ${
                    darkMode ? "border-indigo-400" : "border-indigo-500"
                  } border-4 border-t-transparent animate-spin`}
                ></div>
              </div>
              <h3
                className={`text-xl font-bold mb-2 ${
                  darkMode ? "text-indigo-300" : "text-indigo-700"
                }`}
              >
                جاري تحميل الملف الشخصي...
              </h3>
              <p
                className={`text-sm ${
                  darkMode ? "text-gray-400" : "text-gray-600"
                }`}
              >
                يرجى الانتظار قليلاً...
              </p>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setEditedUser({
      ...editedUser,
      [name]: value,
    });
  };

  const handleProfessionChange = async (field, value) => {
    console.log(`بدء تحديث ${field}:`, value);

    // إذا كان الحقل هو "both"، فهذا يعني أننا نريد تحديث المهن والتخصصات معًا
    if (field === "both" && typeof value === "object") {
      // تحديث البيانات في editedUser
      setEditedUser((prevState) => {
        return {
          ...prevState,
          professions: Array.isArray(value.professions)
            ? [...value.professions]
            : [],
          specializations: Array.isArray(value.specializations)
            ? [...value.specializations]
            : [],
        };
      });

      // إذا كان المستخدم في وضع التعديل، قم بتحديث المهن والتخصصات في الخادم مباشرة
      if (isEditing && userType === "craftsman") {
        try {
          const { craftsmanService } = await import("../../services/api");

          // إعداد بيانات الحرفي للتحديث
          const craftsmanData = {
            professions: value.professions || [],
            specializations: value.specializations || [],
            bio: editedUser.bio || "",
            features: editedUser.features || [],
            workRadius: editedUser.workRadius || 5,
            location: editedUser.location || {},
            workingHours: editedUser.workingHours || {},
          };

          console.log("تحديث بيانات الحرفي في الخادم:", craftsmanData);

          // استدعاء API لتحديث بيانات الحرفي
          const craftsmanResult = await craftsmanService.updateProfile(
            craftsmanData
          );

          console.log("استجابة تحديث بيانات الحرفي:", craftsmanResult);

          // تحديث بيانات المستخدم في المتجر
          const updatedUser = {
            ...user,
            professions: value.professions || [],
            specializations: value.specializations || [],
            features: editedUser.features || [],
            workingHours: editedUser.workingHours || {}, // إضافة ساعات العمل
            skipApiCall: true, // تجنب استدعاء API مرة أخرى
          };

          // تحديث المستخدم في المتجر
          updateUser(updatedUser);

          // إضافة تأثير بصري لتأكيد التحديث
          const professionSelector = document.querySelector(
            ".profession-selector"
          );
          if (professionSelector) {
            professionSelector.classList.add("update-flash");
            setTimeout(() => {
              professionSelector.classList.remove("update-flash");
            }, 1000);
          }
        } catch (error) {
          console.error("خطأ في تحديث بيانات الحرفي مباشرة:", error);
        }
      }
    } else {
      // تحديث البيانات في editedUser
      setEditedUser((prevState) => {
        const updatedUser = {
          ...prevState,
          [field]: Array.isArray(value) ? [...value] : value, // التأكد من استخدام نسخة جديدة من المصفوفة
        };
        return updatedUser;
      });

      // إذا كان المستخدم في وضع التعديل، قم بتحديث المهن والتخصصات في الخادم مباشرة
      if (isEditing && userType === "craftsman") {
        try {
          const { craftsmanService } = await import("../../services/api");

          // استخدام setTimeout للتأكد من أن التحديث قد تم بالفعل
          setTimeout(async () => {
            // الحصول على القيم المحدثة
            const updatedProfessions =
              field === "professions"
                ? Array.isArray(value)
                  ? [...value]
                  : []
                : Array.isArray(editedUser.professions)
                ? [...editedUser.professions]
                : [];

            const updatedSpecializations =
              field === "specializations"
                ? Array.isArray(value)
                  ? [...value]
                  : []
                : Array.isArray(editedUser.specializations)
                ? [...editedUser.specializations]
                : [];

            // إعداد بيانات الحرفي للتحديث
            const craftsmanData = {
              professions: updatedProfessions,
              specializations: updatedSpecializations,
              bio: editedUser.bio || "",
              features: editedUser.features || [],
              workRadius: editedUser.workRadius || 5,
              location: editedUser.location || {},
              workingHours: editedUser.workingHours || {},
            };

            console.log("تحديث بيانات الحرفي في الخادم:", craftsmanData);

            // استدعاء API لتحديث بيانات الحرفي
            const craftsmanResult = await craftsmanService.updateProfile(
              craftsmanData
            );

            console.log("استجابة تحديث بيانات الحرفي:", craftsmanResult);

            // تحديث بيانات المستخدم في المتجر
            const updatedUser = {
              ...user,
              professions: updatedProfessions,
              specializations: updatedSpecializations,
              features: editedUser.features || [],
              workingHours: editedUser.workingHours || {}, // إضافة ساعات العمل
              skipApiCall: true, // تجنب استدعاء API مرة أخرى
            };

            // تحديث المستخدم في المتجر
            updateUser(updatedUser);

            // إضافة تأثير بصري لتأكيد التحديث
            const professionSelector = document.querySelector(
              ".profession-selector"
            );
            if (professionSelector) {
              professionSelector.classList.add("update-flash");
              setTimeout(() => {
                professionSelector.classList.remove("update-flash");
              }, 1000);
            }
          }, 100);
        } catch (error) {
          console.error("خطأ في تحديث بيانات الحرفي مباشرة:", error);
        }
      }
    }
  };

  const handleBioChange = (bio) => {
    setEditedUser({
      ...editedUser,
      bio,
    });
  };

  const handleFeaturesChange = async (features) => {
    console.log("تحديث الخصائص:", features);

    // التأكد من أن الخصائص مصفوفة
    const featuresArray = Array.isArray(features) ? features : [features];

    // دمج الخصائص الجديدة مع الخصائص الحالية
    const currentFeatures = Array.isArray(editedUser.features)
      ? editedUser.features
      : editedUser.features
      ? [editedUser.features]
      : [];

    // استخدام Set لإزالة التكرارات
    const uniqueFeatures = [...new Set([...currentFeatures, ...featuresArray])];

    console.log("الخصائص بعد الدمج:", {
      currentFeatures,
      featuresArray,
      uniqueFeatures,
    });

    setEditedUser({
      ...editedUser,
      features: uniqueFeatures,
    });

    // إذا كان المستخدم في وضع التعديل، قم بتحديث الخصائص في الخادم مباشرة
    if (isEditing && userType === "craftsman") {
      try {
        const { craftsmanService } = await import("../../services/api");

        // إعداد بيانات الحرفي للتحديث
        const craftsmanData = {
          professions: editedUser.professions || [],
          specializations: editedUser.specializations || [],
          bio: editedUser.bio || "",
          features: uniqueFeatures, // استخدام الخصائص المدمجة
          workRadius: editedUser.workRadius || 5,
          location: editedUser.location || {},
          workingHours: editedUser.workingHours || {},
        };

        console.log("تحديث بيانات الحرفي في الخادم:", craftsmanData);

        // استدعاء API لتحديث بيانات الحرفي
        const craftsmanResult = await craftsmanService.updateProfile(
          craftsmanData
        );

        console.log("استجابة تحديث بيانات الحرفي:", craftsmanResult);

        // طباعة الخصائص للتصحيح
        console.log("الخصائص قبل تحديث المستخدم في المتجر:", {
          features: uniqueFeatures,
          featuresType: typeof uniqueFeatures,
          featuresIsArray: Array.isArray(uniqueFeatures),
          featuresLength: uniqueFeatures.length,
          resultFeatures: craftsmanResult.features,
        });

        // تحديث بيانات المستخدم في المتجر
        const updatedUser = {
          ...user,
          features: Array.isArray(craftsmanResult.features)
            ? craftsmanResult.features
            : craftsmanResult.features
            ? [craftsmanResult.features]
            : uniqueFeatures,
          skipApiCall: true, // تجنب استدعاء API مرة أخرى
        };

        // تحديث المستخدم في المتجر
        updateUser(updatedUser);
      } catch (error) {
        console.error("خطأ في تحديث بيانات الحرفي مباشرة:", error);
      }
    }
  };

  const handleLocationChange = async (location) => {
    try {
      // مسح التخزين المؤقت للخريطة
      const mapService = await import("../../services/mapService");
      mapService.clearMapCache();

      // تحديث الموقع في حالة المستخدم المحلية
      setEditedUser({
        ...editedUser,
        location,
      });

      // إذا كان المستخدم في وضع التعديل، قم بتحديث الموقع في الخادم مباشرة
      if (isEditing && userType === "craftsman") {
        try {
          const { craftsmanService } = await import("../../services/api");

          // إعداد بيانات الحرفي للتحديث
          const craftsmanData = {
            professions: editedUser.professions || [],
            specializations: editedUser.specializations || [],
            bio: editedUser.bio || "",
            features: editedUser.features || [],
            workRadius: editedUser.workRadius || 5,
            location: location, // استخدام الموقع الجديد
            workingHours: editedUser.workingHours || {},
            // إضافة الشوارع والمساجد والمستشفيات المحدثة
            streetsInWorkRange: streetsInWorkRange || [],
            hospitalsInWorkRange: hospitalsInWorkRange || [],
            mosquesInWorkRange: mosquesInWorkRange || [],
          };

          console.log("تحديث بيانات الموقع في الخادم:", craftsmanData);

          // استدعاء API لتحديث بيانات الحرفي
          const craftsmanResult = await craftsmanService.updateProfile(
            craftsmanData
          );

          console.log("استجابة تحديث بيانات الموقع:", craftsmanResult);

          // تحديث بيانات المستخدم في المتجر
          if (craftsmanResult && craftsmanResult.location) {
            updateUser({
              ...user,
              location: craftsmanResult.location,
              streetsInWorkRange: craftsmanResult.streetsInWorkRange || [],
              hospitalsInWorkRange: craftsmanResult.hospitalsInWorkRange || [],
              mosquesInWorkRange: craftsmanResult.mosquesInWorkRange || [],
              skipApiCall: true, // تجنب استدعاء API مرة أخرى
            });

            // تحديث المستخدم في localStorage مباشرة
            try {
              const localUser = JSON.parse(
                localStorage.getItem("user") || "{}"
              );
              localUser.location = craftsmanResult.location;
              localUser.streetsInWorkRange =
                craftsmanResult.streetsInWorkRange || [];
              localUser.hospitalsInWorkRange =
                craftsmanResult.hospitalsInWorkRange || [];
              localUser.mosquesInWorkRange =
                craftsmanResult.mosquesInWorkRange || [];
              localStorage.setItem("user", JSON.stringify(localUser));
              console.log("تم تحديث بيانات الموقع في localStorage مباشرة");
            } catch (localStorageError) {
              console.error(
                "خطأ في تحديث بيانات الموقع في localStorage:",
                localStorageError
              );
            }
          }
        } catch (error) {
          console.error("خطأ في تحديث بيانات الموقع مباشرة:", error);
        }
      }
    } catch (error) {
      console.error("خطأ في مسح التخزين المؤقت للخريطة:", error);
    }
  };

  const handleRadiusChange = async (workRadius) => {
    try {
      // مسح التخزين المؤقت للخريطة
      const mapService = await import("../../services/mapService");
      mapService.clearMapCache();

      // تحديث نطاق العمل في حالة المستخدم المحلية
      setEditedUser({
        ...editedUser,
        workRadius,
      });

      // إذا كان المستخدم في وضع التعديل، قم بتحديث نطاق العمل في الخادم مباشرة
      if (isEditing && userType === "craftsman") {
        try {
          const { craftsmanService } = await import("../../services/api");

          // إعداد بيانات الحرفي للتحديث
          const craftsmanData = {
            professions: editedUser.professions || [],
            specializations: editedUser.specializations || [],
            bio: editedUser.bio || "",
            features: editedUser.features || [],
            workRadius: workRadius, // استخدام نطاق العمل الجديد
            location: editedUser.location || {},
            workingHours: editedUser.workingHours || {},
            // إضافة الشوارع والمساجد والمستشفيات المحدثة
            streetsInWorkRange: streetsInWorkRange || [],
            hospitalsInWorkRange: hospitalsInWorkRange || [],
            mosquesInWorkRange: mosquesInWorkRange || [],
          };

          console.log("تحديث بيانات نطاق العمل في الخادم:", craftsmanData);

          // استدعاء API لتحديث بيانات الحرفي
          const craftsmanResult = await craftsmanService.updateProfile(
            craftsmanData
          );

          console.log("استجابة تحديث بيانات نطاق العمل:", craftsmanResult);

          // تحديث بيانات المستخدم في المتجر
          if (craftsmanResult && craftsmanResult.workRadius) {
            updateUser({
              ...user,
              workRadius: craftsmanResult.workRadius,
              streetsInWorkRange: craftsmanResult.streetsInWorkRange || [],
              hospitalsInWorkRange: craftsmanResult.hospitalsInWorkRange || [],
              mosquesInWorkRange: craftsmanResult.mosquesInWorkRange || [],
              skipApiCall: true, // تجنب استدعاء API مرة أخرى
            });

            // تحديث المستخدم في localStorage مباشرة
            try {
              const localUser = JSON.parse(
                localStorage.getItem("user") || "{}"
              );
              localUser.workRadius = craftsmanResult.workRadius;
              localUser.streetsInWorkRange =
                craftsmanResult.streetsInWorkRange || [];
              localUser.hospitalsInWorkRange =
                craftsmanResult.hospitalsInWorkRange || [];
              localUser.mosquesInWorkRange =
                craftsmanResult.mosquesInWorkRange || [];
              localStorage.setItem("user", JSON.stringify(localUser));
              console.log("تم تحديث بيانات نطاق العمل في localStorage مباشرة");
            } catch (localStorageError) {
              console.error(
                "خطأ في تحديث بيانات نطاق العمل في localStorage:",
                localStorageError
              );
            }
          }
        } catch (error) {
          console.error("خطأ في تحديث بيانات نطاق العمل مباشرة:", error);
        }
      }
    } catch (error) {
      console.error("خطأ في مسح التخزين المؤقت للخريطة:", error);
    }
  };

  const handleWorkingHoursChange = (workingHours) => {
    console.log("تحديث ساعات العمل:", workingHours);

    // استخراج أيام العمل المحددة
    const workingDays = Object.entries(workingHours)
      .filter(([_, hours]) => hours.isWorking === true)
      .map(([day, _]) => day);

    console.log("أيام العمل المحددة:", workingDays);

    // استخراج وقت العمل الموحد من أول يوم عمل
    const firstWorkingDay = Object.values(workingHours).find(
      (day) => day?.isWorking === true
    );
    if (firstWorkingDay) {
      setWorkingHoursTime({
        start: firstWorkingDay.start || "",
        end: firstWorkingDay.end || "",
      });
    }

    // تحديث ساعات العمل في حالة المستخدم
    setEditedUser({
      ...editedUser,
      workingHours: workingHours,
    });
  };

  // دالة لحفظ ساعات العمل بشكل منفصل
  const handleSaveWorkingHours = async () => {
    try {
      // استخدام ساعات العمل من حالة المستخدم المحلية
      const workingHours = editedUser.workingHours || {};

      console.log("حفظ ساعات العمل بشكل منفصل:", workingHours);

      // استخراج أيام العمل المحددة
      const workingDays = Object.entries(workingHours)
        .filter(([_, hours]) => hours.isWorking === true)
        .map(([day, _]) => day);

      console.log("أيام العمل المحددة للحفظ:", workingDays);

      // إعداد ساعات العمل للباك إند
      const workingHoursForBackend = {};

      Object.keys(workingHours).forEach((day) => {
        // التأكد من أن قيمة isWorking هي قيمة منطقية (boolean)
        const isWorkingValue = workingHours[day]?.isWorking === true;

        // إضافة الوقت الموحد فقط للأيام المحددة
        workingHoursForBackend[day] = {
          isWorking: isWorkingValue ? true : false, // تأكد من أن القيمة هي قيمة منطقية صريحة
        };

        // إذا كان اليوم يوم عمل، أضف وقت العمل الموحد
        if (isWorkingValue) {
          workingHoursForBackend[day].start =
            workingHours[day].start || workingHoursTime.start;
          workingHoursForBackend[day].end =
            workingHours[day].end || workingHoursTime.end;
        } else {
          // حتى للأيام غير المحددة، أضف وقت افتراضي
          workingHoursForBackend[day].start = "";
          workingHoursForBackend[day].end = "";
        }
      });

      console.log("ساعات العمل المرسلة للباك إند:", workingHoursForBackend);

      // استدعاء API لتحديث ساعات العمل
      const { craftsmanService } = await import("../../services/api");

      // استخدام API ساعات العمل الجديد
      const response = await craftsmanService.updateWorkingHours(
        workingHoursForBackend
      );

      console.log("نتيجة تحديث ساعات العمل:", response);

      // التأكد من أن ساعات العمل المستلمة من الباك إند صحيحة
      if (response.workingHours) {
        // تطبيع ساعات العمل المستلمة من الباك إند
        const normalizedWorkingHours = {};

        Object.entries(response.workingHours).forEach(([day, hours]) => {
          if (hours) {
            // التحقق من أن isWorking هي قيمة منطقية
            const isWorkingValue = !!hours.isWorking;

            // إضافة قيم افتراضية لـ start/end إذا لم تكن موجودة
            normalizedWorkingHours[day] = {
              start: hours.start || "",
              end: hours.end || "",
              isWorking: isWorkingValue,
            };
          }
        });

        // تحديث ساعات العمل في حالة المستخدم
        setEditedUser({
          ...editedUser,
          workingHours: normalizedWorkingHours,
        });

        // تحديث المستخدم في المتجر
        updateUser({
          ...user,
          workingHours: normalizedWorkingHours,
          skipApiCall: true, // تجنب استدعاء API مرة أخرى
        });

        // تحديث ساعات العمل في localStorage مباشرة
        try {
          const localUser = JSON.parse(localStorage.getItem("user") || "{}");
          localUser.workingHours = normalizedWorkingHours;
          localStorage.setItem("user", JSON.stringify(localUser));
          console.log(
            "تم تحديث ساعات العمل في localStorage مباشرة:",
            normalizedWorkingHours
          );
        } catch (localStorageError) {
          console.error(
            "خطأ في تحديث ساعات العمل في localStorage:",
            localStorageError
          );
        }

        // عرض رسالة نجاح باستخدام Toast
        const { showToast } = await import("../../utils/toastUtils");
        showToast("تم حفظ ساعات العمل بنجاح", "success", 1000);
      }
    } catch (error) {
      console.error("خطأ في حفظ ساعات العمل:", error);
      // عرض رسالة خطأ باستخدام Toast
      const { showToast } = await import("../../utils/toastUtils");
      showToast("حدث خطأ أثناء حفظ ساعات العمل", "error", 1000);
    }
  };

  // دالة لتبديل حالة التوفر (غير مستخدمة حاليًا)
  // const handleAvailabilityToggle = () => {
  //   setEditedUser({
  //     ...editedUser,
  //     available: !editedUser.available,
  //   });
  // };

  const handleAddImage = async (files) => {
    try {
      // التحقق من وجود ملفات
      if (
        !files ||
        (files instanceof FileList && files.length === 0) ||
        (Array.isArray(files) && files.length === 0)
      ) {
        console.error("لم يتم اختيار أي ملفات");
        return Promise.reject(new Error("لم يتم اختيار أي ملفات"));
      }

      // إنشاء FormData لإرسال الصور
      const formData = new FormData();

      // إضافة كل ملف إلى FormData باستخدام اسم الحقل الصحيح "galleryImages"
      if (files instanceof FileList) {
        for (let i = 0; i < files.length; i++) {
          formData.append("galleryImages", files[i]);
        }
      } else if (Array.isArray(files)) {
        files.forEach((file) => {
          if (file instanceof File) {
            formData.append("galleryImages", file);
          }
        });
      } else if (files instanceof File) {
        formData.append("galleryImages", files);
      } else {
        // إذا كانت imageUrls عبارة عن URLs فقط (ليست ملفات)، أضفها مباشرة
        const urlsArray = Array.isArray(files) ? files : [files];
        setEditedUser({
          ...editedUser,
          gallery: [...(editedUser.gallery || []), ...urlsArray],
        });
        // نعيد Promise محلول لإخبار المكون الفرعي بأن العملية اكتملت
        return Promise.resolve();
      }

      // استدعاء API لتحميل الصور
      const craftsmanService = (await import("../../services/api"))
        .craftsmanService;

      // إضافة معلومات تصحيح للكونسول
      console.log("FormData المرسلة:", {
        fieldName: "galleryImages",
        filesCount:
          files instanceof FileList
            ? files.length
            : Array.isArray(files)
            ? files.filter((f) => f instanceof File).length
            : 1,
      });

      // نبدأ عملية التحميل
      console.log("بدء تحميل الصور...");

      // إنشاء وعد لتتبع حالة تحميل الصور
      const uploadPromise = new Promise(async (resolve, reject) => {
        try {
          // تحميل الصور إلى الخادم
          const response = await craftsmanService.uploadGalleryImages(formData);
          console.log("تم تحميل صور المعرض بنجاح:", response);

          // إنشاء مستمع لحدث تحميل الصورة
          let loadedImages = 0;
          const totalImages = response.imageUrls
            ? response.imageUrls.length
            : 0;

          if (totalImages === 0) {
            // إذا لم تكن هناك صور، نحل الوعد مباشرة
            resolve(response);
            return;
          }

          // إنشاء مهلة زمنية للتأكد من أن الوعد يتم حله حتى لو لم يتم تحميل جميع الصور
          const timeoutId = setTimeout(() => {
            console.log("انتهت المهلة الزمنية لتحميل الصور، حل الوعد");
            resolve(response);
          }, 10000); // 10 ثوان كحد أقصى

          // إنشاء مستمع لحدث تحميل الصورة
          const imageLoadedHandler = (event) => {
            const loadedSrc = event.detail.src;
            const isNewImage = response.imageUrls.some(
              (url) =>
                loadedSrc.includes(url) ||
                (typeof url === "string" &&
                  loadedSrc.includes(url.split("/").pop()))
            );

            if (isNewImage) {
              loadedImages++;
              console.log(`تم تحميل الصورة ${loadedImages} من ${totalImages}`);

              if (loadedImages >= totalImages) {
                console.log("تم تحميل جميع الصور، حل الوعد");
                clearTimeout(timeoutId);
                window.removeEventListener("imageLoaded", imageLoadedHandler);
                resolve(response);
              }
            }
          };

          window.addEventListener("imageLoaded", imageLoadedHandler);

          // حل الوعد بعد استجابة الخادم، حتى لو لم يتم تحميل جميع الصور بعد
          return response;
        } catch (error) {
          console.error("خطأ في تحميل الصور:", error);
          reject(error);
        }
      });

      const response = await uploadPromise;

      // إذا كان هناك استجابة من الخادم، استخدم معرض الصور المُرجع مباشرة
      if (response) {
        console.log("استجابة الخادم لتحميل الصور:", response);

        // استخدام معرض الصور المُرجع من الخادم مباشرة
        if (response.gallery || response.workGallery) {
          const serverGallery = response.gallery || response.workGallery || [];
          console.log(
            "معرض الصور المستلم من الخادم بعد التحميل:",
            serverGallery
          );

          // تحديث حالة المستخدم المحلية بالبيانات المستلمة من الخادم
          setEditedUser((prev) => ({
            ...prev,
            gallery: serverGallery,
          }));

          // تحديث المستخدم في المتجر أيضًا
          updateUser({
            ...user,
            gallery: serverGallery,
            skipApiCall: true, // تجنب استدعاء API مرة أخرى
          });

          console.log("تم تحديث معرض الأعمال بنجاح من استجابة التحميل");
          return Promise.resolve();
        }

        // إذا لم يكن هناك معرض صور في الاستجابة، استخدم URLs الصور المحملة
        if (response.imageUrls && response.imageUrls.length > 0) {
          console.log("تم استلام URLs للصور المحملة:", response.imageUrls);

          // تصفية أي مسارات فارغة أو غير صالحة
          const validImageUrls = response.imageUrls.filter(
            (url) => url && url !== "undefined" && url !== "null"
          );

          console.log("URLs الصالحة للصور:", validImageUrls);

          // الحصول على معرض الصور الحالي من المستخدم
          const currentGallery = editedUser.gallery || [];
          console.log("معرض الصور الحالي:", currentGallery);

          // دمج الصور الجديدة مع الصور الحالية
          const updatedGallery = [...currentGallery, ...validImageUrls];

          console.log("معرض الصور بعد التحديث:", {
            currentGallery: currentGallery,
            validImageUrls: validImageUrls,
            updatedGallery: updatedGallery,
          });

          // تحديث حالة المستخدم المحلية
          setEditedUser({
            ...editedUser,
            gallery: updatedGallery,
          });

          // تحديث معرض الأعمال في قاعدة البيانات مباشرة
          try {
            console.log(
              "تحديث معرض الأعمال في قاعدة البيانات:",
              updatedGallery
            );
            const { craftsmanService } = await import("../../services/api");

            // استخدام updateGallery لتحديث معرض الأعمال في الخادم
            const updateResponse = await craftsmanService.updateGallery(
              updatedGallery
            );
            console.log("استجابة تحديث معرض الأعمال:", updateResponse);

            // تحديث المعرض في المستخدم المحلي مرة أخرى بالبيانات المستلمة من الخادم
            if (
              updateResponse &&
              (updateResponse.workGallery || updateResponse.gallery)
            ) {
              const serverGallery =
                updateResponse.workGallery || updateResponse.gallery || [];
              console.log("معرض الصور المستلم من الخادم:", serverGallery);

              // تحديث حالة المستخدم المحلية بالبيانات المستلمة من الخادم
              setEditedUser((prev) => ({
                ...prev,
                gallery: serverGallery,
              }));

              // تحديث المستخدم في المتجر أيضًا
              updateUser({
                ...user,
                gallery: serverGallery,
                skipApiCall: true, // تجنب استدعاء API مرة أخرى
              });
            }

            console.log("تم تحديث معرض الأعمال بنجاح");

            // ننتظر لحظة إضافية للتأكد من تحديث واجهة المستخدم
            await new Promise((resolve) => setTimeout(resolve, 500));

            // نعيد Promise محلول لإخبار المكون الفرعي بأن العملية اكتملت
            return Promise.resolve();
          } catch (galleryError) {
            console.error("خطأ في تحديث معرض الأعمال:", galleryError);
            // نعيد Promise مرفوض لإخبار المكون الفرعي بأن هناك خطأ
            return Promise.reject(galleryError);
          }
        }
      }

      // إذا لم يكن هناك URLs للصور، نعيد Promise محلول
      return Promise.resolve();
    } catch (error) {
      console.error("خطأ في تحميل صور المعرض:", error);
      // نعيد Promise مرفوض لإخبار المكون الفرعي بأن هناك خطأ
      return Promise.reject(error);
    }
  };

  const handleProfileImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      try {
        // إنشاء كائن FormData لإرسال الصورة
        const formData = new FormData();
        formData.append("profileImage", file);

        // عرض معاينة محلية أثناء التحميل
        const reader = new FileReader();
        reader.onloadend = () => {
          // تحديث الصورة في editedUser فقط للمعاينة المحلية
          setEditedUser({
            ...editedUser,
            image: reader.result, // معاينة محلية
            tempImage: reader.result, // حفظ الصورة المؤقتة
          });

          // تحديث user أيضًا لمنع إعادة تعيين الصورة عند إعادة فتح وضع التعديل
          updateUser({
            ...user,
            image: reader.result,
            tempImage: reader.result,
            skipApiCall: true, // تجنب استدعاء API في هذه المرحلة
          });
        };
        reader.readAsDataURL(file);

        // استيراد خدمة المستخدم إذا لم تكن متوفرة بعد
        const userService = (await import("../../services/api")).userService;

        // تحميل الصورة إلى الخادم
        const response = await userService.uploadProfileImage(formData);
        console.log("Image upload response:", response);

        // تحديث عنوان URL للصورة باستخدام عنوان URL الخادم
        if (response && response.imageUrl) {
          // استيراد SERVER_URL لبناء عنوان URL كامل
          const { SERVER_URL } = await import("../../services/config");
          const fullImageUrl = `${SERVER_URL}${response.imageUrl}`;
          console.log("Full image URL:", fullImageUrl);

          // تحديث بيانات المستخدم المحلية
          const updatedUser = {
            ...editedUser,
            image: response.imageUrl, // استخدام المسار النسبي بدلاً من URL الكامل
            profilePicture: response.imageUrl, // مهم: تحديث profilePicture أيضًا
            tempImage: null, // إزالة الصورة المؤقتة
          };

          // طباعة معلومات الصورة للتصحيح
          console.log("تحديث صورة الملف الشخصي:", {
            originalResponse: response,
            imageUrl: response.imageUrl,
            fullImageUrl: fullImageUrl,
            updatedUserImage: updatedUser.image,
            updatedUserProfilePicture: updatedUser.profilePicture,
          });

          // تحديث editedUser بالصورة الجديدة
          setEditedUser(updatedUser);

          // تحديث user أيضًا لمنع إعادة تعيين الصورة عند إعادة فتح وضع التعديل
          updateUser({
            ...user,
            image: response.imageUrl,
            profilePicture: response.imageUrl,
            tempImage: null,
            skipApiCall: true, // تجنب استدعاء API مرتين
          });

          // تحديث بيانات المستخدم في المتجر مباشرة
          updateUser({
            ...user,
            image: response.imageUrl,
            profilePicture: response.imageUrl,
            skipApiCall: false, // تغيير إلى false لضمان تحديث البيانات في الخادم
          });

          // لا نستخدم localStorage لتخزين بيانات المستخدم
          // بدلاً من ذلك، نعتمد على بيانات المستخدم المخزنة في المتجر (store)
          console.log("تم تحديث صورة المستخدم في المتجر بنجاح");

          // تحديث الملف الشخصي في الخادم مباشرة
          try {
            const profileData = {
              name: editedUser.name,
              email: editedUser.email,
              phone: editedUser.phone,
              profilePicture: response.imageUrl,
              image: response.imageUrl,
            };

            const updateResponse = await userService.updateProfile(profileData);
            console.log("Profile update response:", updateResponse);

            // إرسال حدث تحديث الصورة لإعلام المكونات الأخرى
            try {
              const imageUpdateEvent = new CustomEvent("userImageUpdated", {
                detail: {
                  userId: user.id || user._id,
                  newImagePath: response.imageUrl,
                },
              });
              window.dispatchEvent(imageUpdateEvent);
              console.log("Dispatched userImageUpdated event");
            } catch (eventError) {
              console.log(
                "Error dispatching image update event:",
                eventError.message
              );
            }
          } catch (updateError) {
            console.error("Error updating profile in server:", updateError);
          }

          console.log("تم تحديث صورة المستخدم بنجاح في جميع الأماكن");
        }
      } catch (error) {
        console.error("Error uploading profile image:", error);
        // الاحتفاظ بالمعاينة المحلية في حالة حدوث خطأ
      }
    }
  };

  const handleRemoveImage = async (index) => {
    console.log("حذف الصورة بالفهرس:", index);

    // الحصول على المعرض الحالي
    const currentGallery = [...(editedUser.gallery || [])];
    console.log("معرض الصور قبل الحذف:", currentGallery);

    // حفظ مسار الصورة المحذوفة للتصحيح
    const removedImage = currentGallery[index];
    console.log("الصورة المحذوفة:", removedImage);

    // حذف الصورة من المصفوفة
    currentGallery.splice(index, 1);
    console.log("معرض الصور بعد الحذف:", currentGallery);

    // تحديث حالة المستخدم المحلية
    setEditedUser({
      ...editedUser,
      gallery: currentGallery,
    });

    // تحديث معرض الأعمال في قاعدة البيانات بعد الحذف
    try {
      console.log(
        "تحديث معرض الأعمال في قاعدة البيانات بعد الحذف:",
        currentGallery
      );
      const { craftsmanService } = await import("../../services/api");

      // استخدام updateGallery لتحديث معرض الأعمال في الخادم
      const updateResponse = await craftsmanService.updateGallery(
        currentGallery
      );
      console.log("استجابة تحديث معرض الأعمال بعد الحذف:", updateResponse);

      // تحديث المعرض في المستخدم المحلي مرة أخرى بالبيانات المستلمة من الخادم
      if (
        updateResponse &&
        (updateResponse.workGallery || updateResponse.gallery)
      ) {
        const serverGallery =
          updateResponse.workGallery || updateResponse.gallery || [];
        console.log("معرض الصور المستلم من الخادم بعد الحذف:", serverGallery);

        // تحديث حالة المستخدم المحلية بالبيانات المستلمة من الخادم
        setEditedUser((prev) => ({
          ...prev,
          gallery: serverGallery,
        }));

        // تحديث المستخدم في المتجر أيضًا
        updateUser({
          ...user,
          gallery: serverGallery,
          skipApiCall: true, // تجنب استدعاء API مرة أخرى
        });
      }

      console.log("تم تحديث معرض الأعمال بعد الحذف بنجاح");
    } catch (error) {
      console.error("خطأ في تحديث معرض الأعمال بعد الحذف:", error);
    }
  };

  // التحقق من صحة البيانات
  const validateForm = () => {
    const newErrors = {};
    let isValid = true;
    let firstErrorField = null;

    // التحقق من الاسم
    if (!editedUser.name || editedUser.name.trim() === "") {
      newErrors.name = "الاسم مطلوب";
      firstErrorField = firstErrorField || "name";
      isValid = false;
    }

    // التحقق من البريد الإلكتروني
    if (!editedUser.email || editedUser.email.trim() === "") {
      newErrors.email = "البريد الإلكتروني مطلوب";
      firstErrorField = firstErrorField || "email";
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(editedUser.email)) {
      newErrors.email = "يرجى إدخال بريد إلكتروني صالح";
      firstErrorField = firstErrorField || "email";
      isValid = false;
    }

    // التحقق من رقم الهاتف
    if (!editedUser.phone || editedUser.phone.trim() === "") {
      newErrors.phone = "رقم الهاتف مطلوب";
      firstErrorField = firstErrorField || "phone";
      isValid = false;
    }

    // إذا كان المستخدم حرفي، تحقق من المهن والتخصصات
    if (userType === "craftsman") {
      if (!editedUser.professions || editedUser.professions.length === 0) {
        newErrors.professions = "يجب اختيار مهنة واحدة على الأقل";
        firstErrorField = firstErrorField || "professions";
        isValid = false;
      }

      if (
        !editedUser.specializations ||
        editedUser.specializations.length === 0
      ) {
        newErrors.specializations = "يجب اختيار تخصص واحد على الأقل";
        firstErrorField =
          firstErrorField || firstErrorField === "professions"
            ? "professions"
            : "specializations";
        isValid = false;
      }
    }

    setErrors(newErrors);

    // إذا كان هناك خطأ، قم بالتمرير إلى الحقل الذي يحتوي على الخطأ الأول
    if (!isValid && firstErrorField) {
      setTimeout(() => {
        // البحث عن الحقل بالاسم
        const errorElement = document.querySelector(
          `[name="${firstErrorField}"]`
        );

        if (errorElement) {
          // التمرير إلى الحقل
          errorElement.scrollIntoView({ behavior: "smooth", block: "center" });
          // تركيز الحقل
          errorElement.focus();
          // إضافة تأثير وميض للحقل
          errorElement.classList.add("error-flash");
          // إزالة التأثير بعد ثانيتين
          setTimeout(() => {
            errorElement.classList.remove("error-flash");
          }, 2000);
        } else if (
          firstErrorField === "professions" ||
          firstErrorField === "specializations"
        ) {
          // إذا كان الخطأ في المهن أو التخصصات، ابحث عن مكون ProfessionSelector
          const professionSelector = document.querySelector(
            ".profession-selector"
          );
          if (professionSelector) {
            professionSelector.scrollIntoView({
              behavior: "smooth",
              block: "center",
            });
          }
        }
      }, 100);
    }

    return isValid;
  };

  const handleSaveChanges = async () => {
    // التحقق من صحة البيانات قبل الحفظ
    if (!validateForm()) {
      return;
    }

    setIsSaving(true);
    try {
      // تنظيف البيانات القديمة قبل الحفظ
      const updatedUserData = { ...editedUser };

      // إزالة الحقول القديمة إذا كانت موجودة
      if (userType === "craftsman") {
        if (updatedUserData.profession) {
          delete updatedUserData.profession;
        }
        if (updatedUserData.specialization) {
          delete updatedUserData.specialization;
        }

        // تحويل gallery إلى workGallery للتوافق مع الباك إند
        if (updatedUserData.gallery) {
          updatedUserData.workGallery = updatedUserData.gallery;
          console.log(
            "تم تحويل gallery إلى workGallery:",
            updatedUserData.workGallery
          );
        }
      }

      // إذا كان هناك صورة جديدة، تأكد من تحديث حقل profilePicture
      if (updatedUserData.image && updatedUserData.image !== user.image) {
        updatedUserData.profilePicture = updatedUserData.image;
      }

      // حفظ معرف المستخدم الأصلي
      updatedUserData.id = user.id || user._id;

      // طباعة البيانات قبل الإرسال للتصحيح
      console.log("بيانات المستخدم قبل الإرسال للتحديث:", {
        id: updatedUserData.id,
        name: updatedUserData.name,
        email: updatedUserData.email,
        image: updatedUserData.image ? "موجودة" : "غير موجودة",
        profilePicture: updatedUserData.profilePicture
          ? "موجودة"
          : "غير موجودة",
      });

      // استدعاء API لتحديث الملف الشخصي
      const userService = (await import("../../services/api")).userService;
      const result = await userService.updateProfile(updatedUserData);

      console.log("نتيجة تحديث الملف الشخصي:", result);

      // تحديث الاسم في Supabase إذا كان هناك تغيير في الاسم
      if (updatedUserData.name !== user.name) {
        try {
          // استيراد الملف بشكل صحيح
          const supabaseAuthService = (
            await import("../../services/supabaseAuthService")
          ).default;
          await supabaseAuthService.updateUserProfile({
            full_name: updatedUserData.name,
            display_name: updatedUserData.name,
          });
          console.log(
            "تم تحديث الاسم في Supabase بنجاح:",
            updatedUserData.name
          );
        } catch (supabaseError) {
          console.error("خطأ في تحديث الاسم في Supabase:", supabaseError);
        }
      }

      // إذا كان المستخدم حرفي، قم بتحديث بيانات الحرفي أيضًا
      if (userType === "craftsman") {
        try {
          const { craftsmanService } = await import("../../services/api");

          // طباعة الخصائص للتصحيح
          console.log("الخصائص قبل تحديث بيانات الحرفي:", {
            features: updatedUserData.features,
            featuresType: typeof updatedUserData.features,
            featuresIsArray: Array.isArray(updatedUserData.features),
            featuresLength: updatedUserData.features
              ? updatedUserData.features.length
              : 0,
          });

          // إعداد بيانات الحرفي للتحديث
          // إعداد ساعات العمل للباك إند باستخدام نموذج الوقت الموحد
          const workingHoursForBackend = {};

          if (updatedUserData.workingHours) {
            console.log(
              "ساعات العمل قبل الإرسال للباك إند:",
              updatedUserData.workingHours
            );

            // استخراج أيام العمل المحددة
            const workingDays = Object.entries(updatedUserData.workingHours)
              .filter(([_, hours]) => hours.isWorking === true)
              .map(([day, _]) => day);

            console.log(
              "أيام العمل المحددة قبل الإرسال للباك إند:",
              workingDays
            );

            // استخدام وقت العمل الموحد لجميع الأيام
            Object.keys(updatedUserData.workingHours).forEach((day) => {
              // التأكد من أن قيمة isWorking هي قيمة منطقية (boolean)
              const isWorkingValue =
                updatedUserData.workingHours[day]?.isWorking === true;

              // إضافة الوقت الموحد فقط للأيام المحددة
              workingHoursForBackend[day] = {
                isWorking: isWorkingValue ? true : false, // تأكد من أن القيمة هي قيمة منطقية صريحة
              };

              // إذا كان اليوم يوم عمل، أضف وقت العمل الموحد
              if (isWorkingValue) {
                workingHoursForBackend[day].start = workingHoursTime.start;
                workingHoursForBackend[day].end = workingHoursTime.end;
              } else {
                // حتى للأيام غير المحددة، أضف وقت افتراضي
                workingHoursForBackend[day].start = "";
                workingHoursForBackend[day].end = "";
              }
            });

            // طباعة ساعات العمل المرسلة للباك إند بشكل مفصل
            console.log(
              "تفاصيل ساعات العمل المرسلة للباك إند:",
              JSON.stringify(workingHoursForBackend)
            );
          }

          // طباعة النبذة قبل إرسالها للباك إند
          console.log("النبذة قبل إرسالها للباك إند:", {
            bio: updatedUserData.bio,
            bioType: typeof updatedUserData.bio,
            bioLength: updatedUserData.bio ? updatedUserData.bio.length : 0,
            bioEmpty: updatedUserData.bio === "",
            bioUndefined: updatedUserData.bio === undefined,
            bioNull: updatedUserData.bio === null,
          });

          const craftsmanData = {
            professions: updatedUserData.professions || [],
            specializations: updatedUserData.specializations || [],
            bio: updatedUserData.bio || "",
            features: Array.isArray(updatedUserData.features)
              ? updatedUserData.features
              : updatedUserData.features
              ? [updatedUserData.features]
              : [],
            workRadius: updatedUserData.workRadius || 5,
            location: updatedUserData.location || {},
            workingHours: workingHoursForBackend,
            // إضافة الشوارع والمساجد والمستشفيات المحدثة
            streetsInWorkRange:
              streetsInWorkRange || updatedUserData.streetsInWorkRange || [],
            hospitalsInWorkRange:
              hospitalsInWorkRange ||
              updatedUserData.hospitalsInWorkRange ||
              [],
            mosquesInWorkRange:
              mosquesInWorkRange || updatedUserData.mosquesInWorkRange || [],
          };

          console.log("بيانات الأماكن المرسلة للخادم:", {
            streetsInWorkRange: craftsmanData.streetsInWorkRange,
            hospitalsInWorkRange: craftsmanData.hospitalsInWorkRange,
            mosquesInWorkRange: craftsmanData.mosquesInWorkRange,
          });

          // طباعة ساعات العمل للتصحيح
          console.log(
            "ساعات العمل المرسلة للباك إند:",
            craftsmanData.workingHours
          );

          console.log("تحديث بيانات الحرفي:", craftsmanData);

          // استدعاء API لتحديث بيانات الحرفي
          const craftsmanResult = await craftsmanService.updateProfile(
            craftsmanData
          );

          console.log("نتيجة تحديث بيانات الحرفي:", craftsmanResult);

          // طباعة النبذة المستلمة من الباك إند
          console.log("النبذة المستلمة من الباك إند:", {
            bio: craftsmanResult.bio,
            bioType: typeof craftsmanResult.bio,
            bioLength: craftsmanResult.bio ? craftsmanResult.bio.length : 0,
            bioEmpty: craftsmanResult.bio === "",
            bioUndefined: craftsmanResult.bio === undefined,
            bioNull: craftsmanResult.bio === null,
          });

          // طباعة ساعات العمل المستلمة من الباك إند
          console.log(
            "ساعات العمل المستلمة من الباك إند:",
            craftsmanResult.workingHours
          );

          // التأكد من أن ساعات العمل المستلمة من الباك إند صحيحة
          if (craftsmanResult.workingHours) {
            // طباعة ساعات العمل المستلمة من الباك إند
            console.log(
              "ساعات العمل المستلمة من الباك إند:",
              craftsmanResult.workingHours
            );

            // التأكد من أن قيم isWorking هي قيم منطقية (boolean) وإضافة قيم افتراضية لـ start/end
            const normalizedWorkingHours = {};

            // طباعة ساعات العمل المستلمة من الباك إند بشكل مفصل
            console.log(
              "تفاصيل ساعات العمل المستلمة من الباك إند:",
              JSON.stringify(craftsmanResult.workingHours)
            );

            Object.entries(craftsmanResult.workingHours).forEach(
              ([day, hours]) => {
                if (hours) {
                  // التحقق من أن isWorking هي قيمة منطقية
                  // تحويل القيمة إلى قيمة منطقية صريحة باستخدام !!
                  const isWorkingValue = !!hours.isWorking;

                  // إضافة قيم افتراضية لـ start/end إذا لم تكن موجودة
                  normalizedWorkingHours[day] = {
                    start: hours.start || "",
                    end: hours.end || "",
                    isWorking: isWorkingValue,
                  };
                }
              }
            );

            // طباعة حالة أيام العمل للتصحيح
            const workingDays = Object.entries(normalizedWorkingHours)
              .filter(([_, hours]) => hours.isWorking)
              .map(([day, _]) => day);

            console.log("أيام العمل المستلمة من الباك إند:", workingDays);

            // تحديث ساعات العمل في نتيجة الباك إند
            craftsmanResult.workingHours = normalizedWorkingHours;

            // تحديث ساعات العمل في بيانات المستخدم المحدثة
            updatedUserData.workingHours = normalizedWorkingHours;

            console.log(
              "ساعات العمل بعد التحويل:",
              JSON.stringify(normalizedWorkingHours)
            );

            // تحديث وقت العمل الموحد باستخدام أول يوم عمل
            if (workingDays.length > 0) {
              const firstWorkingDay = workingDays[0];
              const firstDayHours = normalizedWorkingHours[firstWorkingDay];
              if (firstDayHours) {
                setWorkingHoursTime({
                  start: firstDayHours.start || "",
                  end: firstDayHours.end || "",
                });
                console.log("تم تحديث وقت العمل الموحد من الباك إند:", {
                  start: firstDayHours.start,
                  end: firstDayHours.end,
                });
              }
            }
          }

          // دمج نتائج تحديث المستخدم والحرفي
          result.professions =
            craftsmanResult.professions || updatedUserData.professions;
          result.specializations =
            craftsmanResult.specializations || updatedUserData.specializations;

          // طباعة النبذة قبل الدمج
          console.log("النبذة قبل الدمج:", {
            bioFromCraftsmanResult: craftsmanResult.bio,
            bioFromUpdatedUserData: updatedUserData.bio,
            bioTypeFromCraftsmanResult: typeof craftsmanResult.bio,
            bioTypeFromUpdatedUserData: typeof updatedUserData.bio,
          });

          result.bio = craftsmanResult.bio || updatedUserData.bio;

          // طباعة النبذة بعد الدمج
          console.log("النبذة بعد الدمج:", {
            bioInResult: result.bio,
            bioType: typeof result.bio,
            bioLength: result.bio ? result.bio.length : 0,
          });
          // التأكد من أن الخصائص مصفوفة
          const featuresFromResult =
            craftsmanResult.features || updatedUserData.features || [];
          result.features = Array.isArray(featuresFromResult)
            ? featuresFromResult
            : [featuresFromResult];

          console.log("الخصائص بعد التحديث:", {
            features: result.features,
            featuresType: typeof result.features,
            featuresIsArray: Array.isArray(result.features),
            featuresLength: result.features.length,
          });
          result.workRadius =
            craftsmanResult.workRadius || updatedUserData.workRadius;
          result.location =
            craftsmanResult.location || updatedUserData.location;
          result.workingHours =
            craftsmanResult.workingHours || updatedUserData.workingHours;

          // طباعة المهن والتخصصات والخصائص بعد الدمج للتأكد من وجودها
          console.log("المهن بعد الدمج:", result.professions);
          console.log("التخصصات بعد الدمج:", result.specializations);
          console.log("الخصائص بعد الدمج:", result.features);
        } catch (craftsmanError) {
          console.error("خطأ في تحديث بيانات الحرفي:", craftsmanError);
        }
      }

      // تحديث حالة المستخدم في المتجر مع الحفاظ على المعرف الأصلي
      const updatedUser = {
        ...result,
        id: user.id || user._id || result.id || result._id,
        image: updatedUserData.image || user.image,
        profilePicture: updatedUserData.profilePicture || user.profilePicture,
        // تضمين النبذة بشكل صريح
        bio: result.bio || updatedUserData.bio || user.bio || "",
        // التأكد من وجود المهن والتخصصات والخصائص
        professions:
          result.professions ||
          updatedUserData.professions ||
          user.professions ||
          [],
        specializations:
          result.specializations ||
          updatedUserData.specializations ||
          user.specializations ||
          [],
        // التأكد من أن الخصائص مصفوفة
        features: (() => {
          const featuresFromResult =
            result.features || updatedUserData.features || user.features || [];
          return Array.isArray(featuresFromResult)
            ? featuresFromResult
            : [featuresFromResult];
        })(),
        // التأكد من وجود ساعات العمل
        workingHours: updatedUserData.workingHours || user.workingHours || {},
      };

      // طباعة المهن والتخصصات والخصائص والنبذة في المستخدم المحدث
      console.log("المهن في المستخدم المحدث:", updatedUser.professions);
      console.log("التخصصات في المستخدم المحدث:", updatedUser.specializations);
      console.log("الخصائص في المستخدم المحدث:", updatedUser.features);
      console.log("النبذة في المستخدم المحدث:", {
        bio: updatedUser.bio,
        bioType: typeof updatedUser.bio,
        bioLength: updatedUser.bio ? updatedUser.bio.length : 0,
      });

      // التأكد من أن المهن والتخصصات هي مصفوفات
      if (!Array.isArray(updatedUser.professions)) {
        console.log("تحويل المهن إلى مصفوفة");
        updatedUser.professions = updatedUser.professions
          ? [updatedUser.professions]
          : [];
      }

      if (!Array.isArray(updatedUser.specializations)) {
        console.log("تحويل التخصصات إلى مصفوفة");
        updatedUser.specializations = updatedUser.specializations
          ? [updatedUser.specializations]
          : [];
      }

      if (!Array.isArray(updatedUser.features)) {
        console.log("تحويل الخصائص إلى مصفوفة");
        updatedUser.features = updatedUser.features
          ? [updatedUser.features]
          : [];
      }

      console.log("بيانات المستخدم بعد التحديث:", updatedUser);

      // تحديث المستخدم في المتجر
      updateUser(updatedUser);

      // لا نستخدم localStorage لتخزين بيانات المستخدم
      // بدلاً من ذلك، نعتمد على بيانات المستخدم المخزنة في المتجر (store)

      setIsEditing(false);
      // إعادة تعيين الأخطاء بعد الحفظ بنجاح
      setErrors({});
    } catch (error) {
      console.error("Error saving changes:", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelEdit = () => {
    // إعادة تعيين editedUser إلى user الحالي
    setEditedUser({ ...user });

    // إذا كان هناك صورة مؤقتة، قم بإزالتها من user
    if (user.tempImage) {
      updateUser({
        ...user,
        tempImage: null,
        skipApiCall: true, // تجنب استدعاء API عند إلغاء التعديل
      });
    }

    setIsEditing(false);
    // إعادة تعيين الأخطاء عند إلغاء التعديل
    setErrors({});
  };

  return (
    <Layout user={user} onLogout={logout}>
      <div
        className={`min-h-screen py-8 ${
          darkMode
            ? "bg-gradient-to-b from-gray-900 to-gray-800"
            : "bg-gradient-to-br from-blue-50 to-indigo-100"
        } transition-colors duration-300`}
      >
        <div className="container mx-auto px-4">
          {/* هيدر الصفحة مع تأثيرات زخرفية */}
          <div className="relative mb-8">
            <div
              className={`absolute inset-0 rounded-xl ${
                darkMode
                  ? "bg-gradient-to-r from-indigo-900/50 to-purple-900/50"
                  : "bg-gradient-to-r from-blue-500/10 to-indigo-500/10"
              } blur-xl opacity-70 transform -skew-y-1`}
            ></div>
            <div className="relative flex justify-between items-center p-6 rounded-xl overflow-hidden shadow-lg">
              {/* خلفية متدرجة */}
              <div
                className={`absolute inset-0 ${
                  darkMode
                    ? "bg-gradient-to-r from-indigo-900 via-indigo-800 to-purple-900"
                    : "bg-gradient-to-r from-blue-500 via-indigo-500 to-blue-600"
                } opacity-90`}
              ></div>

              {/* أشكال زخرفية */}
              <div className="absolute inset-0 overflow-hidden">
                {/* دوائر زخرفية متحركة */}
                <div className="absolute top-5 right-10 w-24 h-24 rounded-full bg-white opacity-5 animate-pulse"></div>
                <div
                  className="absolute top-20 right-20 w-16 h-16 rounded-full bg-white opacity-5 animate-pulse"
                  style={{ animationDelay: "1s" }}
                ></div>
                <div
                  className="absolute bottom-5 left-10 w-20 h-20 rounded-full bg-white opacity-5 animate-pulse"
                  style={{ animationDelay: "1.5s" }}
                ></div>

                {/* خطوط زخرفية */}
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white to-transparent opacity-20"></div>
              </div>

              {/* عنوان الصفحة */}
              <div className="relative z-10">
                <h1 className="text-3xl font-bold text-white relative inline-block">
                  <span className="relative z-10">ملفي الشخصي</span>
                  <span className="absolute bottom-0 left-0 right-0 h-3 bg-white opacity-20 transform -rotate-1 z-0"></span>
                </h1>
                {userType === "craftsman" && (
                  <p className="text-white/80 mt-1">
                    إدارة ملفك الشخصي كحرفي وتحديث معلوماتك
                  </p>
                )}
              </div>

              {/* أزرار التحكم */}
              <div className="relative z-10">
                {isEditing ? (
                  <div className="flex gap-2">
                    <Button
                      variant="secondary"
                      onClick={handleCancelEdit}
                      disabled={isSaving}
                      className={`transition-all duration-300 shadow-md hover:shadow-lg py-2.5 px-5 relative overflow-hidden group rounded-lg ${
                        darkMode
                          ? "bg-gray-800/80 border border-gray-700 text-white hover:bg-gray-700"
                          : "bg-white/90 border border-indigo-200 text-indigo-700 hover:bg-indigo-50"
                      } ${isSaving ? "opacity-50 cursor-not-allowed" : ""}`}
                    >
                      <span className="relative z-10 font-bold">إلغاء</span>
                      <span
                        className={`absolute inset-0 ${
                          darkMode ? "bg-gray-600" : "bg-indigo-50"
                        } opacity-0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full group-hover:opacity-100 transition-all duration-700`}
                      ></span>
                    </Button>
                    <LoadingButton
                      variant="primary"
                      onClick={handleSaveChanges}
                      isLoading={isSaving}
                      loadingText="جاري حفظ التغييرات..."
                      className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-2.5 px-5 rounded-lg ${
                        darkMode
                          ? "bg-gradient-to-r from-indigo-500 via-indigo-600 to-purple-700 hover:from-indigo-600 hover:via-indigo-700 hover:to-purple-800"
                          : "bg-gradient-to-r from-blue-400 via-indigo-500 to-blue-500 hover:from-blue-500 hover:via-indigo-600 hover:to-blue-600"
                      }`}
                    >
                      <span className="relative z-10 font-bold text-white">
                        حفظ التغييرات
                      </span>
                      <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                      <span className="absolute inset-0 opacity-0 group-hover:opacity-20 bg-gradient-to-r from-yellow-400 via-pink-500 to-indigo-500 transition-opacity duration-700"></span>
                    </LoadingButton>
                  </div>
                ) : (
                  <Button
                    variant="primary"
                    onClick={() => setIsEditing(true)}
                    className={`text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-2.5 px-5 rounded-lg ${
                      darkMode
                        ? "bg-gradient-to-r from-indigo-500 via-indigo-600 to-purple-700 hover:from-indigo-600 hover:via-indigo-700 hover:to-purple-800"
                        : "bg-gradient-to-r from-blue-400 via-indigo-500 to-blue-500 hover:from-blue-500 hover:via-indigo-600 hover:to-blue-600"
                    }`}
                  >
                    <span className="relative z-10 font-bold text-white">
                      تعديل الملف
                    </span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                    <span className="absolute inset-0 opacity-0 group-hover:opacity-20 bg-gradient-to-r from-yellow-400 via-pink-500 to-indigo-500 transition-opacity duration-700"></span>
                  </Button>
                )}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Left Column */}
            <div className="md:col-span-1">
              <ProfileHeader user={editedUser} userType={userType} />

              <div className="mt-4">
                <ProfileInfo user={editedUser} />
              </div>

              {isEditing && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  className="mt-4"
                >
                  <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg shadow-lg p-4 border border-indigo-200">
                    <h3 className="font-bold text-lg mb-4 text-indigo-800 relative inline-block">
                      <span className="relative z-10">
                        تعديل المعلومات الشخصية
                      </span>
                      <span
                        className={`absolute bottom-0 left-0 right-0 h-2 ${
                          darkMode ? "bg-indigo-500" : "bg-indigo-300"
                        } opacity-40 transform -rotate-1 z-0`}
                      ></span>
                    </h3>

                    <div className="mb-8">
                      <label
                        className={`block ${
                          darkMode ? "text-indigo-300" : "text-indigo-700"
                        } font-bold mb-3 text-lg relative inline-block`}
                      >
                        <span className="relative z-10">الصورة الشخصية</span>
                        <span
                          className={`absolute bottom-0 left-0 right-0 h-1 ${
                            darkMode ? "bg-indigo-600" : "bg-indigo-400"
                          } opacity-40 transform -rotate-1 z-0 rounded-full`}
                        ></span>
                      </label>
                      <div className="flex flex-col md:flex-row items-center">
                        <div className="relative w-32 h-32 mx-auto md:mx-0 md:ml-6 mb-4 md:mb-0">
                          {/* حلقات خارجية متحركة */}
                          <div className="absolute inset-0 rounded-full border-4 border-blue-300/40 animate-pulse"></div>
                          <div
                            className="absolute inset-0 rounded-full border-2 border-indigo-400/30 animate-pulse"
                            style={{ animationDuration: "3s" }}
                          ></div>

                          {/* تأثير الهالة */}
                          <div className="absolute inset-0 rounded-full bg-indigo-500/20 filter blur-md"></div>

                          {/* الصورة الرئيسية */}
                          <div
                            className="relative w-full h-full rounded-full overflow-hidden border-4 transform hover:scale-105 transition-all duration-500 bg-white"
                            style={{
                              borderColor: darkMode ? "#4338C8" : "#6366f1",
                              boxShadow: darkMode
                                ? "0 0 25px rgba(67, 56, 200, 0.5), inset 0 0 15px rgba(79, 70, 229, 0.2)"
                                : "0 0 30px rgba(79, 70, 229, 0.4), inset 0 0 10px rgba(99, 102, 241, 0.1)",
                            }}
                          >
                            {/* تأثير الإضاءة */}
                            <div className="absolute inset-0 bg-gradient-to-tr from-indigo-500/10 to-transparent"></div>

                            <SimpleLazyImage
                              src={editedUser.image}
                              alt={editedUser.name}
                              className="w-full h-full object-cover"
                              placeholderClassName="w-full h-full bg-gray-200 animate-pulse"
                            />

                            {/* تأثير التوهج عند التحويم */}
                            <div className="absolute inset-0 bg-indigo-500/0 hover:bg-indigo-500/10 transition-colors duration-300"></div>
                          </div>
                        </div>
                        <div className="flex flex-col items-center md:items-start">
                          <input
                            type="file"
                            id="profileImage"
                            accept="image/*"
                            onChange={handleProfileImageChange}
                            className="hidden"
                          />
                          <label
                            htmlFor="profileImage"
                            className={`cursor-pointer text-white flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transition-all duration-300 relative overflow-hidden group py-2.5 px-5 rounded-lg mb-3 ${
                              darkMode
                                ? "bg-gradient-to-r from-indigo-600 via-indigo-700 to-purple-800 hover:from-indigo-700 hover:via-indigo-800 hover:to-purple-900"
                                : "bg-gradient-to-r from-blue-500 via-indigo-600 to-blue-600 hover:from-blue-600 hover:via-indigo-700 hover:to-blue-700"
                            }`}
                          >
                            <span className="relative z-10 font-bold text-white">
                              تغيير الصورة
                            </span>
                            <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                            <span className="absolute inset-0 opacity-0 group-hover:opacity-20 bg-gradient-to-r from-yellow-400 via-pink-500 to-indigo-500 transition-opacity duration-700"></span>
                          </label>
                          <p
                            className={`${
                              darkMode ? "text-indigo-300" : "text-indigo-600"
                            } text-sm bg-indigo-500/10 p-2 rounded-lg border ${
                              darkMode
                                ? "border-indigo-800/50"
                                : "border-indigo-200"
                            }`}
                          >
                            يفضل استخدام صورة دائرية بدقة عالية
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                      <div
                        className={`p-4 rounded-xl ${
                          darkMode
                            ? "bg-gray-800/80 border border-indigo-900/50"
                            : "bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100"
                        } transition-all duration-300 shadow-md hover:shadow-lg`}
                      >
                        <Input
                          label="الاسم الكامل"
                          name="name"
                          value={editedUser.name}
                          onChange={handleInputChange}
                          error={errors.name}
                          required
                          className={`${
                            darkMode
                              ? "bg-gray-700 border-gray-600 text-white"
                              : "bg-white border-indigo-200"
                          } focus:ring-2 focus:ring-indigo-500 transition-all duration-300`}
                        />
                      </div>

                      <div
                        className={`p-4 rounded-xl ${
                          darkMode
                            ? "bg-gray-800/80 border border-indigo-900/50"
                            : "bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100"
                        } transition-all duration-300 shadow-md hover:shadow-lg`}
                      >
                        <Input
                          label="رقم الهاتف"
                          name="phone"
                          value={editedUser.phone}
                          onChange={handleInputChange}
                          error={errors.phone}
                          required
                          className={`${
                            darkMode
                              ? "bg-gray-700 border-gray-600 text-white"
                              : "bg-white border-indigo-200"
                          } focus:ring-2 focus:ring-indigo-500 transition-all duration-300`}
                        />
                      </div>
                    </div>

                    <div
                      className={`p-4 rounded-xl mb-6 ${
                        darkMode
                          ? "bg-gray-800/80 border border-indigo-900/50"
                          : "bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100"
                      } transition-all duration-300 shadow-md hover:shadow-lg`}
                    >
                      <Input
                        label="البريد الإلكتروني"
                        name="email"
                        value={editedUser.email}
                        onChange={handleInputChange}
                        error={errors.email}
                        required
                        className={`${
                          darkMode
                            ? "bg-gray-700 border-gray-600 text-white"
                            : "bg-white border-indigo-200"
                        } focus:ring-2 focus:ring-indigo-500 transition-all duration-300`}
                      />
                    </div>

                    {userType === "craftsman" && (
                      <div
                        className={`p-4 rounded-xl mb-6 ${
                          darkMode
                            ? "bg-gray-800/80 border border-indigo-900/50"
                            : "bg-gradient-to-br from-indigo-50 to-blue-50 border border-indigo-100"
                        } transition-all duration-300 shadow-md hover:shadow-lg`}
                      >
                        <div className="mb-3">
                          <h3
                            className={`font-bold text-lg ${
                              darkMode ? "text-indigo-300" : "text-indigo-700"
                            } relative inline-block`}
                          >
                            <span className="relative z-10">
                              المهن والتخصصات
                            </span>
                            <span
                              className={`absolute bottom-0 left-0 right-0 h-1 ${
                                darkMode ? "bg-indigo-600" : "bg-indigo-400"
                              } opacity-40 transform -rotate-1 z-0 rounded-full`}
                            ></span>
                          </h3>
                          <p
                            className={`mt-1 text-sm ${
                              darkMode ? "text-gray-400" : "text-gray-600"
                            }`}
                          >
                            اختر المهن والتخصصات التي تقدمها
                          </p>
                        </div>
                        <ProfessionSelector
                          professions={editedUser.professions || []}
                          specializations={editedUser.specializations || []}
                          onChange={handleProfessionChange}
                          errors={errors}
                        />
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </div>

            {/* Right Column */}
            <div className="md:col-span-2">
              {userType === "craftsman" && (
                <>
                  {/* نبذة عني - تظهر فقط في وضع التعديل */}
                  {isEditing ? (
                    <ProfileBio
                      bio={editedUser.bio}
                      features={editedUser.features || []}
                      isEditing={isEditing}
                      onBioChange={handleBioChange}
                      onFeaturesChange={handleFeaturesChange}
                    />
                  ) : null}

                  {/* معرض الأعمال المطور */}
                  <WorkGalleryManager
                    key={`gallery-${user?.id || user?._id}-${isEditing}`}
                    isEditable={isEditing}
                    maxImages={20}
                    title="معرض الأعمال"
                  />

                  {/* معرض الأعمال القديم (معلق مؤقتاً) */}
                  {/* {editedUser.gallery && editedUser.gallery.length > 0 && (
                    <div className="mt-6">
                      <ProfileGallery
                        gallery={editedUser.gallery}
                        isEditing={isEditing}
                        onAddImage={handleAddImage}
                        onRemoveImage={handleRemoveImage}
                      />
                    </div>
                  )} */}

                  {/* الموقع ونطاق العمل - يظهر فقط في وضع التعديل */}
                  {isEditing ? (
                    <ProfileLocation
                      location={editedUser.location}
                      workRadius={editedUser.workRadius}
                      isEditing={isEditing}
                      onLocationChange={handleLocationChange}
                      onRadiusChange={handleRadiusChange}
                      streetsInWorkRange={editedUser.streetsInWorkRange || []}
                      hospitalsInWorkRange={
                        editedUser.hospitalsInWorkRange || []
                      }
                      mosquesInWorkRange={editedUser.mosquesInWorkRange || []}
                    />
                  ) : null}

                  <ProfileWorkingHours
                    workingHours={
                      editedUser.workingHours || {
                        saturday: {
                          isWorking: false,
                          start: "",
                          end: "",
                        },
                        sunday: {
                          isWorking: false,
                          start: "",
                          end: "",
                        },
                        monday: {
                          isWorking: false,
                          start: "",
                          end: "",
                        },
                        tuesday: {
                          isWorking: false,
                          start: "",
                          end: "",
                        },
                        wednesday: {
                          isWorking: false,
                          start: "",
                          end: "",
                        },
                        thursday: {
                          isWorking: false,
                          start: "",
                          end: "",
                        },
                        friday: {
                          isWorking: false,
                          start: "",
                          end: "",
                        },
                      }
                    }
                    workingHoursTime={workingHoursTime}
                    isEditing={isEditing}
                    onWorkingHoursChange={handleWorkingHoursChange}
                    onWorkingHoursTimeChange={setWorkingHoursTime}
                    onSaveWorkingHours={handleSaveWorkingHours}
                    darkMode={darkMode}
                  />
                </>
              )}

              <div
                className={`p-6 mb-6 rounded-lg shadow-lg ${
                  darkMode
                    ? "bg-gradient-to-br from-gray-800 to-indigo-900/40 border border-indigo-800/30"
                    : "bg-gradient-to-br from-blue-50 to-indigo-100 border border-indigo-100/50"
                } transition-all duration-300 hover:shadow-xl relative overflow-hidden group`}
              >
                {/* خلفية زخرفية */}
                <div className="absolute inset-0 bg-gradient-to-tr from-indigo-500/5 to-transparent"></div>
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-indigo-400/30 to-transparent"></div>
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-indigo-400/30 to-transparent"></div>

                {/* زخرفة جانبية */}
                <div className="absolute top-0 right-0 h-full w-1 bg-gradient-to-b from-transparent via-indigo-400/20 to-transparent"></div>

                {/* محتوى القسم */}
                <div className="relative z-10">
                  {/* عنوان القسم */}
                  <div className="flex items-center mb-1">
                    <div
                      className={`p-2.5 rounded-full ${
                        darkMode ? "bg-indigo-700/30" : "bg-indigo-100"
                      } ml-3 shadow-md`}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className={`h-6 w-6 ${
                          darkMode ? "text-indigo-300" : "text-indigo-600"
                        }`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                        />
                      </svg>
                    </div>
                    <div>
                      <h2
                        className={`text-xl font-bold ${
                          darkMode ? "text-indigo-300" : "text-indigo-800"
                        } relative inline-block transition-colors duration-300`}
                      >
                        <span className="relative z-10">طلباتي</span>
                        <span
                          className={`absolute bottom-0 left-0 right-0 h-2 ${
                            darkMode ? "bg-indigo-500" : "bg-indigo-300"
                          } opacity-40 transform -rotate-1 z-0`}
                        ></span>
                      </h2>
                      <p
                        className={`text-sm mt-1 ${
                          darkMode ? "text-indigo-400" : "text-indigo-600"
                        } transition-colors duration-300`}
                      >
                        {userType === "client"
                          ? "إدارة طلبات الخدمة الخاصة بك"
                          : ""}
                      </p>
                    </div>
                  </div>

                  {/* قائمة الميزات المتسلسلة */}
                  <div
                    className={`p-5 rounded-lg mb-5 ${
                      darkMode
                        ? "bg-indigo-900/20 border border-indigo-800/30"
                        : "bg-white/80 border border-indigo-100"
                    } backdrop-blur-sm`}
                  >
                    <h3
                      className={`font-bold text-base ${
                        darkMode ? "text-blue-300" : "text-blue-700"
                      } mb-3 flex items-center`}
                    >
                      <span
                        className={`inline-flex items-center justify-center w-6 h-6 rounded-full ${
                          darkMode ? "bg-blue-800/50" : "bg-blue-100"
                        } ml-2 text-sm`}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className={`h-4 w-4 ${
                            darkMode ? "text-blue-300" : "text-blue-600"
                          }`}
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </span>
                      إدارة طلباتك بسهولة
                    </h3>

                    {/* قائمة الميزات */}
                    <ul
                      className={`space-y-3 pr-8 mt-4 relative ${
                        darkMode ? "text-gray-300" : "text-gray-600"
                      }`}
                    >
                      {/* خط عمودي للتسلسل */}
                      <div
                        className={`absolute right-3 top-1 bottom-1 w-0.5 ${
                          darkMode ? "bg-indigo-800/50" : "bg-indigo-200/70"
                        }`}
                      ></div>

                      {/* العنصر الأول */}
                      <li className="relative flex items-start">
                        <span
                          className={`absolute right-[-27.8px] top-0 w-4 h-4 rounded-full ${
                            darkMode ? "bg-indigo-700" : "bg-indigo-500"
                          } mt-1 shadow-sm z-10`}
                        ></span>
                        <div
                          className={`p-3 rounded-lg ${
                            darkMode ? "bg-gray-800/50" : "bg-indigo-50/50"
                          } border ${
                            darkMode ? "border-gray-700" : "border-indigo-100"
                          } w-full transition-all duration-300 hover:shadow-md`}
                        >
                          <span className="font-medium block mb-1">
                            {userType === "client"
                              ? "متابعة حالة الطلبات"
                              : "عرض طلبات العملاء"}
                          </span>
                          <span className="text-sm block opacity-90">
                            {userType === "client"
                              ? "يمكنك متابعة حالة طلباتك ومعرفة ما إذا كانت قيد الانتظار أو تمت الموافقة عليها أو مكتملة."
                              : "يمكنك الاطلاع على جميع طلبات العملاء المقدمة إليك وتصفيتها حسب الحالة."}
                          </span>
                        </div>
                      </li>

                      {/* العنصر الثاني */}
                      <li className="relative flex items-start">
                        <span
                          className={`absolute right-[-27.8px] top-0 w-4 h-4 rounded-full ${
                            darkMode ? "bg-blue-700" : "bg-blue-500"
                          } mt-1 shadow-sm z-10`}
                        ></span>
                        <div
                          className={`p-3 rounded-lg ${
                            darkMode ? "bg-gray-800/50" : "bg-blue-50/50"
                          } border ${
                            darkMode ? "border-gray-700" : "border-blue-100"
                          } w-full transition-all duration-300 hover:shadow-md`}
                        >
                          <span className="font-medium block mb-1">
                            {userType === "client"
                              ? "تعديل أو إلغاء الطلبات"
                              : "قبول أو رفض الطلبات"}
                          </span>
                          <span className="text-sm block opacity-90">
                            {userType === "client"
                              ? "يمكنك تعديل تفاصيل طلباتك أو إلغائها خلال فترة زمنية محددة بعد تقديمها."
                              : "يمكنك قبول طلبات العملاء أو رفضها وتحديد مواعيد الخدمة المناسبة لك."}
                          </span>
                        </div>
                      </li>

                      {/* العنصر الثالث */}
                      <li className="relative flex items-start">
                        <span
                          className={`absolute right-[-27.8px] top-0 w-4 h-4 rounded-full ${
                            darkMode ? "bg-green-700" : "bg-green-500"
                          } mt-1 shadow-sm z-10`}
                        ></span>
                        <div
                          className={`p-3 rounded-lg ${
                            darkMode ? "bg-gray-800/50" : "bg-green-50/50"
                          } border ${
                            darkMode ? "border-gray-700" : "border-green-100"
                          } w-full transition-all duration-300 hover:shadow-md`}
                        >
                          <span className="font-medium block mb-1">
                            {userType === "client"
                              ? "تقييم الخدمات المكتملة"
                              : "تحديث حالة الطلبات"}
                          </span>
                          <span className="text-sm block opacity-90">
                            {userType === "client"
                              ? "بعد اكتمال الخدمة، يمكنك تقييم الحرفي وكتابة مراجعة عن جودة الخدمة المقدمة."
                              : "يمكنك تحديث حالة الطلبات إلى مكتملة بعد الانتهاء من تقديم الخدمة للعميل."}
                          </span>
                        </div>
                      </li>
                    </ul>
                  </div>

                  {/* أزرار الإجراءات */}
                  <div className="flex flex-wrap gap-3 justify-center md:justify-start">
                    <Link to="/bookings">
                      <Button
                        variant="primary"
                        className={`text-white flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2.5 px-5 rounded-lg ${
                          darkMode
                            ? "bg-gradient-to-r from-indigo-700 to-purple-800 hover:from-indigo-800 hover:to-purple-900"
                            : "bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                        }`}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                          />
                        </svg>
                        <span className="relative z-10 font-bold text-white">
                          عرض جميع الطلبات
                        </span>
                        <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                      </Button>
                    </Link>

                    {userType === "client" && (
                      <Link to="/search">
                        <Button
                          variant="secondary"
                          className={`flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all duration-200 relative overflow-hidden group py-2.5 px-5 rounded-lg ${
                            darkMode
                              ? "bg-gray-700 text-indigo-300 hover:bg-gray-600 border border-indigo-700/30"
                              : "bg-white text-indigo-700 hover:bg-indigo-50 border border-indigo-200"
                          }`}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                            />
                          </svg>
                          <span className="relative z-10 font-bold">
                            البحث عن حرفي
                          </span>
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default MyProfilePage;
