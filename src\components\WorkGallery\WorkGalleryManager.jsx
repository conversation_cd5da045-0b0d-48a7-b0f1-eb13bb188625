import React, { useState, useEffect } from "react";
import { Plus, Grid, List, RefreshCw } from "lucide-react";
import useThemeStore from "../../store/themeStore";
import { craftsmanService } from "../../services/api";
import { showToast } from "../../utils/toast";
import WorkGalleryUpload from "./WorkGalleryUpload";
import WorkGalleryDisplay from "./WorkGalleryDisplay";
import { getImageUrl } from "../../services/imageUploadService";

const WorkGalleryManager = ({
  craftsmanId = null,
  isEditable = false,
  maxImages = 20,
  title = "معرض الأعمال",
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [images, setImages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showUpload, setShowUpload] = useState(false);
  const [viewMode, setViewMode] = useState("grid"); // grid or list
  const [columns, setColumns] = useState(3);

  // جلب الصور عند تحميل المكون
  useEffect(() => {
    loadGallery();
  }, [craftsmanId]);

  // إعادة تحميل المعرض عند تغيير حالة التحرير
  useEffect(() => {
    if (isEditable) {
      // تأخير قصير لضمان تحميل البيانات
      const timer = setTimeout(() => {
        loadGallery();
      }, 500);
      return () => clearTimeout(timer);
    }
  }, [isEditable]);

  const loadGallery = async () => {
    try {
      setLoading(true);
      console.log("WorkGalleryManager: بدء تحميل معرض الأعمال...", {
        craftsmanId,
        isEditable,
      });

      let response;

      if (craftsmanId) {
        // جلب معرض حرفي محدد
        console.log("WorkGalleryManager: جلب معرض حرفي محدد:", craftsmanId);
        response = await craftsmanService.getCraftsmanGallery(craftsmanId);
      } else {
        // جلب معرض الحرفي الحالي
        console.log("WorkGalleryManager: جلب معرض الحرفي الحالي");
        response = await craftsmanService.getCraftsmanGallery("me");
      }

      console.log("WorkGalleryManager: استجابة الخادم:", response);

      // تحويل البيانات إلى تنسيق موحد
      const galleryImages = response.workGallery || [];
      console.log(
        "WorkGalleryManager: صور المعرض الخام:",
        galleryImages.length,
        galleryImages
      );

      // تحويل الصور إلى تنسيق موحد
      const normalizedImages = galleryImages
        .map((image, index) => {
          if (typeof image === "string") {
            const normalizedUrl = getImageUrl(image);
            console.log(
              `تطبيع URL مباشر ${index}:`,
              image,
              "->",
              normalizedUrl
            );
            return {
              id: `legacy_${index}`,
              url: normalizedUrl,
              thumb: normalizedUrl,
              medium: normalizedUrl,
              filename: `work-image-${index + 1}.jpg`,
              size: 0,
              uploadedAt: new Date().toISOString(),
            };
          }

          // إذا كان ObjectId فقط (مشكلة في الباك إند)
          if (
            typeof image === "object" &&
            image._id &&
            Object.keys(image).length <= 2
          ) {
            console.warn(`صورة تحتوي على ObjectId فقط، تجاهل: ${image._id}`);
            return null;
          }

          // إذا كان كائن لكن لا يحتوي على url، ابحث عن مسار الصورة
          if (typeof image === "object" && !image.url) {
            // البحث عن مسار الصورة في خصائص الكائن
            const possiblePaths = Object.values(image).filter(
              (value) =>
                typeof value === "string" &&
                (value.startsWith("/uploads/") ||
                  value.startsWith("http") ||
                  value.includes("i.ibb.co"))
            );

            if (possiblePaths.length > 0) {
              const normalizedUrl = getImageUrl(possiblePaths[0]);
              console.log(
                `تطبيع مسار من كائن ${index}:`,
                possiblePaths[0],
                "->",
                normalizedUrl
              );
              return {
                id: image._id || image.id || `object_${index}`,
                url: normalizedUrl,
                thumb: normalizedUrl,
                medium: normalizedUrl,
                filename: image.filename || `work-image-${index + 1}.jpg`,
                size: image.size || 0,
                uploadedAt: image.uploadedAt || new Date().toISOString(),
              };
            } else {
              // إذا لم نجد مسار صورة، تجاهل هذا العنصر
              console.warn(
                `تجاهل صورة بدون مسار صالح في الفهرس ${index}:`,
                image
              );
              return null;
            }
          }

          // إذا كان كائن صالح مع url
          const normalizedImage = {
            id: image._id || image.id || `normalized_${index}`,
            url: getImageUrl(image.url || image.display_url),
            thumb: getImageUrl(image.thumb || image.url || image.display_url),
            medium: getImageUrl(image.medium || image.url || image.display_url),
            filename: image.filename || `work-image-${index + 1}.jpg`,
            size: image.size || 0,
            uploadedAt: image.uploadedAt || new Date().toISOString(),
          };

          console.log(`تطبيع الصورة ${index}:`, {
            original: image,
            normalized: normalizedImage,
          });

          return normalizedImage;
        })
        .filter((image) => image !== null); // تصفية العناصر الفارغة

      console.log(
        "WorkGalleryManager: الصور المطبعة النهائية:",
        normalizedImages.length,
        normalizedImages
      );
      setImages(normalizedImages);

      if (normalizedImages.length > 0) {
        console.log(
          "WorkGalleryManager: تم تحميل معرض الأعمال بنجاح مع",
          normalizedImages.length,
          "صورة"
        );
      } else {
        console.log("WorkGalleryManager: معرض الأعمال فارغ");
      }
    } catch (error) {
      console.error("WorkGalleryManager: خطأ في جلب معرض الأعمال:", error);
      console.error(
        "WorkGalleryManager: تفاصيل الخطأ:",
        error.response?.data || error.message
      );
      showToast("فشل في جلب معرض الأعمال", "error", 3000);
      setImages([]);
    } finally {
      setLoading(false);
    }
  };

  const handleImagesUploaded = async (newImages) => {
    try {
      console.log("إضافة صور جديدة:", newImages);

      // تحديث معرض الأعمال في الخادم
      const currentImages = [...images, ...newImages];
      // إرسال الكائنات الكاملة بدلاً من URLs فقط
      const imageData = currentImages.map((img) => {
        // إذا كان الكائن يحتوي على بيانات كاملة، أرسله كما هو
        if (typeof img === "object" && img.url) {
          return img;
        }
        // إذا كان نص، حوله إلى كائج
        if (typeof img === "string") {
          return { url: img };
        }
        return img;
      });

      const response = await craftsmanService.updateGallery(imageData);
      console.log("استجابة الخادم:", response);

      if (response.success || response.craftsman) {
        // استخدام البيانات المطبعة من الخادم إذا كانت متوفرة
        const updatedGallery =
          response.workGallery || response.gallery || currentImages;
        setImages(updatedGallery);
        setShowUpload(false);
        showToast(`تم إضافة ${newImages.length} صور بنجاح`, "success", 3000);
      } else {
        showToast(
          response.message || "فشل في حفظ الصور في الخادم",
          "error",
          3000
        );
      }
    } catch (error) {
      console.error("خطأ في حفظ الصور:", error);
      showToast(error.message || "فشل في حفظ الصور", "error", 3000);
    }
  };

  // eslint-disable-next-line no-unused-vars
  const handleDeleteImage = async (imageToDelete, index) => {
    try {
      // إزالة الصورة من القائمة المحلية
      const updatedImages = images.filter((_, i) => i !== index);
      // إرسال الكائنات الكاملة بدلاً من URLs فقط
      const imageData = updatedImages.map((img) => {
        // إذا كان الكائن يحتوي على بيانات كاملة، أرسله كما هو
        if (typeof img === "object" && img.url) {
          return img;
        }
        // إذا كان نص، حوله إلى كائن
        if (typeof img === "string") {
          return { url: img };
        }
        return img;
      });

      // تحديث معرض الأعمال في الخادم
      const response = await craftsmanService.updateGallery(imageData);

      if (response.success || response.craftsman) {
        // استخدام البيانات المطبعة من الخادم إذا كانت متوفرة
        const updatedGallery =
          response.workGallery || response.gallery || updatedImages;
        setImages(updatedGallery);
        return Promise.resolve();
      } else {
        throw new Error(response.message || "فشل في حذف الصورة");
      }
    } catch (error) {
      console.error("خطأ في حذف الصورة:", error);
      throw error;
    }
  };

  const handleClearGallery = async () => {
    const confirmed = window.confirm(
      "هل أنت متأكد من حذف جميع الصور؟ هذا الإجراء لا يمكن التراجع عنه."
    );

    if (confirmed) {
      try {
        // مسح معرض الأعمال في الخادم
        const response = await craftsmanService.updateGallery([]);

        if (response.success || response.craftsman) {
          // استخدام البيانات المطبعة من الخادم إذا كانت متوفرة
          const updatedGallery = response.workGallery || response.gallery || [];
          setImages(updatedGallery);
          showToast("تم مسح معرض الأعمال بالكامل", "success", 3000);
        } else {
          showToast("فشل في مسح معرض الأعمال", "error", 3000);
        }
      } catch (error) {
        console.error("خطأ في مسح معرض الأعمال:", error);
        showToast("فشل في مسح معرض الأعمال", "error", 3000);
      }
    }
  };

  if (loading) {
    return (
      <div
        className={`p-6 rounded-lg ${
          darkMode ? "bg-gray-800" : "bg-white"
        } shadow-md`}
      >
        <div className="flex items-center justify-center py-12">
          <RefreshCw className="w-8 h-8 animate-spin text-blue-500" />
          <span className={`mr-3 ${darkMode ? "text-white" : "text-gray-900"}`}>
            جاري تحميل معرض الأعمال...
          </span>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`p-6 rounded-lg ${
        darkMode ? "bg-gray-800" : "bg-white"
      } shadow-md`}
    >
      {/* رأس المعرض */}
      <div className="flex items-center justify-between mb-6">
        <h2
          className={`text-2xl font-bold ${
            darkMode ? "text-white" : "text-gray-900"
          }`}
        >
          {title}
        </h2>

        <div className="flex items-center space-x-3 space-x-reverse">
          {/* أزرار التحكم في العرض */}
          <div className="flex items-center space-x-1 space-x-reverse">
            <button
              onClick={() => setViewMode("grid")}
              className={`p-2 rounded ${
                viewMode === "grid"
                  ? "bg-blue-500 text-white"
                  : darkMode
                  ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                  : "bg-gray-200 text-gray-600 hover:bg-gray-300"
              }`}
              title="عرض شبكي"
            >
              <Grid className="w-4 h-4" />
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`p-2 rounded ${
                viewMode === "list"
                  ? "bg-blue-500 text-white"
                  : darkMode
                  ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                  : "bg-gray-200 text-gray-600 hover:bg-gray-300"
              }`}
              title="عرض قائمة"
            >
              <List className="w-4 h-4" />
            </button>
          </div>

          {/* اختيار عدد الأعمدة */}
          {viewMode === "grid" && (
            <select
              value={columns}
              onChange={(e) => setColumns(Number(e.target.value))}
              className={`px-3 py-1 rounded border ${
                darkMode
                  ? "bg-gray-700 border-gray-600 text-white"
                  : "bg-white border-gray-300 text-gray-900"
              }`}
            >
              <option value={2}>عمودين</option>
              <option value={3}>3 أعمدة</option>
              <option value={4}>4 أعمدة</option>
            </select>
          )}

          {/* أزرار الإجراءات */}
          {isEditable && (
            <>
              <button
                onClick={() => setShowUpload(!showUpload)}
                className="flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
              >
                <Plus className="w-4 h-4 ml-2" />
                إضافة صور
              </button>

              {images.length > 0 && (
                <button
                  onClick={handleClearGallery}
                  className="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  مسح الكل
                </button>
              )}
            </>
          )}

          {/* زر التحديث */}
          <button
            onClick={loadGallery}
            className={`p-2 rounded ${
              darkMode
                ? "bg-gray-700 text-gray-300 hover:bg-gray-600"
                : "bg-gray-200 text-gray-600 hover:bg-gray-300"
            }`}
            title="تحديث"
          >
            <RefreshCw className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* منطقة رفع الصور */}
      {isEditable && showUpload && (
        <div className="mb-6">
          <WorkGalleryUpload
            onImagesUploaded={handleImagesUploaded}
            maxImages={maxImages}
            existingImages={images}
          />
        </div>
      )}

      {/* عرض الصور */}
      <WorkGalleryDisplay
        images={images}
        onDeleteImage={isEditable ? handleDeleteImage : null}
        isEditable={isEditable}
        showImageCount={true}
        columns={viewMode === "grid" ? columns : 1}
      />

      {/* إحصائيات المعرض */}
      {images.length > 0 && (
        <div
          className={`mt-6 pt-4 border-t ${
            darkMode ? "border-gray-700" : "border-gray-200"
          }`}
        >
          <div
            className={`text-sm ${
              darkMode ? "text-gray-400" : "text-gray-600"
            }`}
          >
            <p>
              إجمالي الصور: {images.length} من {maxImages}
            </p>
            {isEditable && (
              <p>يمكنك إضافة {maxImages - images.length} صور إضافية</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkGalleryManager;
