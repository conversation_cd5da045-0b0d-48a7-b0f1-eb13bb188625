import React, { useState, useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { Briefcase, ArrowLeft } from "lucide-react";
import Card from "../../../components/common/Card";
import Button from "../../../components/common/Button";
import useThemeStore from "../../../store/themeStore";

const AvailableProfessions = () => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [professions, setProfessions] = useState([]);
  const [loading, setLoading] = useState(true);

  // بيانات المهن مع الصور المتحركة
  const professionsData = [
    {
      id: 1,
      name: "ميكانيكي",
      description: "إصلاح وصيانة السيارات والآلات",
      image: "https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop&crop=center",
      color: "from-gray-600 to-gray-800",
      icon: "🔧"
    },
    {
      id: 2,
      name: "مصمم ديكور",
      description: "تصميم وتنفيذ الديكورات الداخلية",
      image: "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=center",
      color: "from-pink-500 to-rose-600",
      icon: "🎨"
    },
    {
      id: 3,
      name: "دهان",
      description: "طلاء وتجديد الجدران والأسطح",
      image: "https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400&h=300&fit=crop&crop=center",
      color: "from-purple-500 to-indigo-600",
      icon: "🎨"
    },
    {
      id: 4,
      name: "نجار",
      description: "صنع وإصلاح الأثاث الخشبي",
      image: "https://images.unsplash.com/photo-1504148455328-c376907d081c?w=400&h=300&fit=crop&crop=center",
      color: "from-orange-500 to-amber-600",
      icon: "🪚"
    },
    {
      id: 5,
      name: "سباك",
      description: "تركيب وإصلاح أنظمة المياه والصرف",
      image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center",
      color: "from-blue-500 to-cyan-600",
      icon: "🔧"
    },
    {
      id: 6,
      name: "كهربائي",
      description: "تركيب وصيانة الأنظمة الكهربائية",
      image: "https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400&h=300&fit=crop&crop=center",
      color: "from-yellow-500 to-orange-600",
      icon: "⚡"
    },
    {
      id: 7,
      name: "مزارع",
      description: "الزراعة والعناية بالنباتات والمحاصيل",
      image: "https://images.unsplash.com/photo-1574943320219-553eb213f72d?w=400&h=300&fit=crop&crop=center",
      color: "from-green-500 to-emerald-600",
      icon: "🌱"
    },
    {
      id: 8,
      name: "بناء",
      description: "بناء وتشييد المباني والمنشآت",
      image: "https://images.unsplash.com/photo-1504307651254-35680f356dfd?w=400&h=300&fit=crop&crop=center",
      color: "from-gray-700 to-slate-800",
      icon: "🏗️"
    },
    {
      id: 9,
      name: "حداد",
      description: "تشكيل وإصلاح المعادن والحديد",
      image: "https://images.unsplash.com/photo-1504328345606-18bbc8c9d7d1?w=400&h=300&fit=crop&crop=center",
      color: "from-red-600 to-orange-700",
      icon: "🔨"
    }
  ];

  useEffect(() => {
    // محاكاة تحميل البيانات
    const loadProfessions = async () => {
      setLoading(true);
      // تأخير لمحاكاة التحميل
      await new Promise(resolve => setTimeout(resolve, 1000));
      setProfessions(professionsData);
      setLoading(false);
    };

    loadProfessions();
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section className="mb-8">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center">
          <Briefcase
            size={24}
            className={`ml-2 ${darkMode ? "text-indigo-400" : "text-indigo-600"}`}
          />
          <h2
            className={`text-2xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300`}
          >
            <span className="relative z-10">المهن المتوفرة</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-2 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0`}
            ></span>
          </h2>
        </div>
        <p className={`text-sm ${darkMode ? "text-gray-400" : "text-gray-600"}`}>
          اكتشف مجموعة متنوعة من المهن والخدمات المتاحة على منصتنا
        </p>
      </div>

      {loading ? (
        <Card className="p-8">
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
            <p className={`${
              darkMode ? "text-indigo-300" : "text-indigo-700"
            } font-medium text-lg transition-colors duration-300`}>
              جاري تحميل المهن...
            </p>
          </div>
        </Card>
      ) : (
        <>
          <motion.div
            className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 md:gap-6"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
          >
            {professions.map((profession) => (
              <motion.div
                key={profession.id}
                variants={itemVariants}
                whileHover={{ 
                  y: -5, 
                  scale: 1.05,
                  transition: { duration: 0.2 }
                }}
                whileTap={{ scale: 0.95 }}
              >
                <Link
                  to={`/search?profession=${profession.name}`}
                  className={`group flex flex-col items-center p-4 h-full rounded-xl ${
                    darkMode
                      ? "bg-gray-800/50 hover:bg-gray-800/80 border border-gray-700"
                      : "bg-white/80 hover:bg-white border border-indigo-100"
                  } shadow-md hover:shadow-lg transition-all duration-300`}
                >
                  {/* صورة المهنة */}
                  <div className="relative w-16 h-16 mb-3 overflow-hidden rounded-full">
                    <img
                      src={profession.image}
                      alt={profession.name}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                      loading="lazy"
                    />
                    <div className={`absolute inset-0 bg-gradient-to-br ${profession.color} opacity-20 group-hover:opacity-30 transition-opacity duration-300`}></div>
                  </div>

                  {/* اسم المهنة */}
                  <h3
                    className={`text-sm font-semibold text-center mb-1 ${
                      darkMode ? "text-gray-200" : "text-gray-800"
                    } group-hover:${darkMode ? "text-indigo-300" : "text-indigo-600"} transition-colors duration-300`}
                  >
                    {profession.name}
                  </h3>

                  {/* وصف المهنة */}
                  <p
                    className={`text-xs text-center ${
                      darkMode ? "text-gray-400" : "text-gray-600"
                    } line-clamp-2 transition-colors duration-300`}
                  >
                    {profession.description}
                  </p>
                </Link>
              </motion.div>
            ))}
          </motion.div>

          {/* زر عرض جميع المهن */}
          <div className="flex justify-center mt-8">
            <Button
              as={Link}
              to="/search"
              variant="outline"
              className={`group flex items-center gap-2 px-6 py-3 ${
                darkMode
                  ? "border-indigo-400 text-indigo-400 hover:bg-indigo-400 hover:text-gray-900"
                  : "border-indigo-600 text-indigo-600 hover:bg-indigo-600 hover:text-white"
              } transition-all duration-300`}
            >
              <span>استكشاف جميع المهن</span>
              <ArrowLeft 
                size={16} 
                className="group-hover:translate-x-1 transition-transform duration-300" 
              />
            </Button>
          </div>
        </>
      )}
    </section>
  );
};

export default AvailableProfessions;
