import React, { useState } from "react";
import {
  X,
  Trash2,
  Eye,
  ChevronLeft,
  ChevronRight,
  Download,
} from "lucide-react";
import useThemeStore from "../../store/themeStore";
import { getImageUrl } from "../../services/imageUploadService";
import { showToast } from "../../utils/toast";

const WorkGalleryDisplay = ({
  images = [],
  onDeleteImage = null,
  isEditable = false,
  showImageCount = true,
  columns = 3,
}) => {
  const darkMode = useThemeStore((state) => state.darkMode);
  const [selectedImage, setSelectedImage] = useState(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const openLightbox = (image, index) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
  };

  const closeLightbox = () => {
    setSelectedImage(null);
    setCurrentImageIndex(0);
  };

  const navigateImage = (direction) => {
    const newIndex =
      direction === "next"
        ? (currentImageIndex + 1) % images.length
        : (currentImageIndex - 1 + images.length) % images.length;

    setCurrentImageIndex(newIndex);
    setSelectedImage(images[newIndex]);
  };

  const handleDeleteImage = async (image, index) => {
    if (!onDeleteImage) return;

    const confirmed = window.confirm("هل أنت متأكد من حذف هذه الصورة؟");
    if (confirmed) {
      try {
        await onDeleteImage(image, index);
        showToast("تم حذف الصورة بنجاح", "success", 3000);

        // إغلاق الـ lightbox إذا كانت الصورة المحذوفة مفتوحة
        if (selectedImage && selectedImage.url === image.url) {
          closeLightbox();
        }
      } catch (error) {
        console.error("خطأ في حذف الصورة:", error);
        showToast("فشل في حذف الصورة", "error", 3000);
      }
    }
  };

  const downloadImage = async (image) => {
    try {
      const response = await fetch(image.url);
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = image.filename || `work-gallery-${Date.now()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      showToast("تم تحميل الصورة", "success", 2000);
    } catch (error) {
      console.error("خطأ في تحميل الصورة:", error);
      showToast("فشل في تحميل الصورة", "error", 3000);
    }
  };

  if (!images || images.length === 0) {
    return (
      <div
        className={`
        text-center py-12 rounded-lg border-2 border-dashed
        ${
          darkMode
            ? "border-gray-600 bg-gray-800 text-gray-400"
            : "border-gray-300 bg-gray-50 text-gray-500"
        }
      `}
      >
        <Eye className="w-12 h-12 mx-auto mb-4 opacity-50" />
        <p className="text-lg font-medium">لا توجد صور في المعرض</p>
        <p className="text-sm mt-1">قم بإضافة صور لعرض أعمالك</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      {/* عداد الصور */}
      {showImageCount && (
        <div
          className={`mb-4 text-sm ${
            darkMode ? "text-gray-300" : "text-gray-600"
          }`}
        >
          {images.length} صورة في المعرض
        </div>
      )}

      {/* شبكة الصور */}
      <div
        className={`
        grid gap-4
        ${columns === 2 ? "grid-cols-2" : ""}
        ${columns === 3 ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3" : ""}
        ${columns === 4 ? "grid-cols-2 sm:grid-cols-3 lg:grid-cols-4" : ""}
      `}
      >
        {images.map((image, index) => (
          <div
            key={image.id || index}
            className={`
              relative group rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-all duration-200
              ${darkMode ? "bg-gray-800" : "bg-white"}
            `}
          >
            {/* الصورة */}
            <div className="aspect-square relative overflow-hidden">
              <img
                src={getImageUrl(image.url || image, "medium")}
                alt={image.filename || `صورة ${index + 1}`}
                className="w-full h-full object-cover cursor-pointer transition-transform duration-200 group-hover:scale-105"
                onClick={() => openLightbox(image, index)}
                loading="lazy"
                onError={(e) => {
                  console.error("خطأ في تحميل الصورة:", {
                    originalImage: image,
                    processedUrl: getImageUrl(image.url || image, "medium"),
                    imageType: typeof image,
                    imageKeys: typeof image === "object" ? Object.keys(image) : "not an object"
                  });
                  e.target.src = "/img/default-avatar.svg";
                }}
              />

              {/* طبقة التحكم */}
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-200 flex items-center justify-center">
                <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2 space-x-reverse">
                  {/* زر العرض */}
                  <button
                    onClick={() => openLightbox(image, index)}
                    className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                    title="عرض الصورة"
                  >
                    <Eye className="w-4 h-4 text-gray-700" />
                  </button>

                  {/* زر التحميل */}
                  <button
                    onClick={() => downloadImage(image)}
                    className="p-2 bg-white bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                    title="تحميل الصورة"
                  >
                    <Download className="w-4 h-4 text-gray-700" />
                  </button>

                  {/* زر الحذف (للمالك فقط) */}
                  {isEditable && onDeleteImage && (
                    <button
                      onClick={() => handleDeleteImage(image, index)}
                      className="p-2 bg-red-500 bg-opacity-90 rounded-full hover:bg-opacity-100 transition-all"
                      title="حذف الصورة"
                    >
                      <Trash2 className="w-4 h-4 text-white" />
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* معلومات الصورة */}
            {(image.filename || image.size) && (
              <div className={`p-3 ${darkMode ? "bg-gray-800" : "bg-white"}`}>
                {image.filename && (
                  <p
                    className={`text-sm font-medium truncate ${
                      darkMode ? "text-white" : "text-gray-900"
                    }`}
                  >
                    {image.filename}
                  </p>
                )}
                {image.size && (
                  <p
                    className={`text-xs ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    {(image.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Lightbox */}
      {selectedImage && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-90">
          <div className="relative max-w-4xl max-h-full p-4">
            {/* أزرار التحكم */}
            <div className="absolute top-4 right-4 z-10 flex space-x-2 space-x-reverse">
              <button
                onClick={() => downloadImage(selectedImage)}
                className="p-2 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 transition-all"
                title="تحميل الصورة"
              >
                <Download className="w-5 h-5 text-white" />
              </button>
              <button
                onClick={closeLightbox}
                className="p-2 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 transition-all"
                title="إغلاق"
              >
                <X className="w-5 h-5 text-white" />
              </button>
            </div>

            {/* الصورة */}
            <img
              src={getImageUrl(selectedImage.url || selectedImage, "original")}
              alt={selectedImage.filename || "صورة من المعرض"}
              className="max-w-full max-h-full object-contain"
            />

            {/* أزرار التنقل */}
            {images.length > 1 && (
              <>
                <button
                  onClick={() => navigateImage("prev")}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 transition-all"
                  title="الصورة السابقة"
                >
                  <ChevronLeft className="w-6 h-6 text-white" />
                </button>
                <button
                  onClick={() => navigateImage("next")}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-white bg-opacity-20 rounded-full hover:bg-opacity-30 transition-all"
                  title="الصورة التالية"
                >
                  <ChevronRight className="w-6 h-6 text-white" />
                </button>
              </>
            )}

            {/* مؤشر الصورة الحالية */}
            {images.length > 1 && (
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white bg-opacity-20 rounded-full px-3 py-1">
                <span className="text-white text-sm">
                  {currentImageIndex + 1} من {images.length}
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default WorkGalleryDisplay;
