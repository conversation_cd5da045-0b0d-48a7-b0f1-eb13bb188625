/* تنسيقات صفحة الترحيب */

/* تنسيق عام للصفحة */
.welcome-page {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

/* حاويات الأقسام */
.section-container {
  position: relative;
  width: 100%;
}

/* تأثير الانتقال بين الأقسام */
.section-container::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 100%;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(99, 102, 241, 0.3),
    transparent
  );
  z-index: 10;
}

/* تأثير الظل بين الأقسام */
.section-container:not(:last-child) {
  box-shadow: 0 4px 15px -8px rgba(0, 0, 0, 0.1);
}

/* تنسيق خاص للوضع الداكن */
.dark-mode .section-container:not(:last-child) {
  box-shadow: 0 4px 15px -8px rgba(0, 0, 0, 0.3);
}

/* تأثير التمرير للأقسام */
@media (min-width: 768px) {
  .section-container {
    scroll-margin-top: 80px;
  }
}

/* تنسيق للعناوين الرئيسية */
.section-title {
  position: relative;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 100%;
  height: 3px;
  background: linear-gradient(
    90deg,
    rgba(99, 102, 241, 0.8),
    rgba(79, 70, 229, 0.3)
  );
  border-radius: 3px;
}

/* تنسيق للبطاقات والعناصر المتحركة */
.feature-card {
  transition: all 0.3s ease;
  border-radius: 12px;
  overflow: hidden;
}

.feature-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.dark-mode .feature-card:hover {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* تأثيرات الخلفية */
.bg-pattern {
  background-image: radial-gradient(
    rgba(99, 102, 241, 0.1) 1px,
    transparent 1px
  );
  background-size: 20px 20px;
}

.dark-mode .bg-pattern {
  background-image: radial-gradient(
    rgba(99, 102, 241, 0.2) 1px,
    transparent 1px
  );
}

/* تنسيق للأزرار */
.cta-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.cta-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.7s ease;
}

.cta-button:hover::after {
  left: 100%;
}

/* تنسيق للروابط */
.section-link {
  position: relative;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.section-link::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: currentColor;
  transition: width 0.3s ease;
}

.section-link:hover::after {
  width: 100%;
}

/* تنسيق للقوائم */
.feature-list {
  list-style-type: none;
  padding-left: 0;
}

.feature-list li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.5rem;
}

.feature-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #6366f1;
  font-weight: bold;
}

/* تأثير التمرير للصفحة */
html {
  scroll-behavior: smooth;
}

/* تنسيق للصور */
.section-image {
  transition: all 0.5s ease;
  border-radius: 12px;
  overflow: hidden;
}

.section-image:hover {
  /* Sin efecto de movimiento */
}

/* أنيميشن دوران بطيء للأيقونات */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 3s linear infinite;
}

/* أنيميشن نبض للأيقونات */
@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* أنيميشن نبض دائري للخلفية */
@keyframes pulse-circle {
  0% {
    transform: scale(0.8);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.3;
  }
}

.animate-pulse-circle {
  animation: pulse-circle 2s ease-in-out infinite;
}

/* تنسيق زر تبديل الوضع المظلم/الفاتح */
.theme-toggle-btn {
  position: relative;
}

/* تأثير الوميض للزر */
.glow-yellow {
  box-shadow: 0 0 15px 2px rgba(253, 224, 71, 0.4);
  animation: glow-pulse-yellow 3s ease-in-out infinite;
}

.glow-blue {
  box-shadow: 0 0 15px 2px rgba(79, 70, 229, 0.4);
  animation: glow-pulse-blue 3s ease-in-out infinite;
}

@keyframes glow-pulse-yellow {
  0% {
    box-shadow: 0 0 10px 1px rgba(253, 224, 71, 0.3);
  }
  50% {
    box-shadow: 0 0 20px 4px rgba(253, 224, 71, 0.5);
  }
  100% {
    box-shadow: 0 0 10px 1px rgba(253, 224, 71, 0.3);
  }
}

@keyframes glow-pulse-blue {
  0% {
    box-shadow: 0 0 10px 1px rgba(79, 70, 229, 0.3);
  }
  50% {
    box-shadow: 0 0 20px 4px rgba(79, 70, 229, 0.5);
  }
  100% {
    box-shadow: 0 0 10px 1px rgba(79, 70, 229, 0.3);
  }
}
