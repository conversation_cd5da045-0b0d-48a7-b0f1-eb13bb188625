// خدمة API للتواصل مع الواجهة الخلفية
import axios from "axios";
import { API_URL } from "./config";

// إنشاء نسخة من axios مع الإعدادات الافتراضية
const api = axios.create({
  baseURL: API_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// إضافة معترض للطلبات لإضافة رمز المصادقة
api.interceptors.request.use(
  (config) => {
    // التحقق من نوع الطلب (أدمن أم مستخدم عادي)
    let token;

    if (config.url && config.url.startsWith("/admin")) {
      // طلبات الأدمن
      token = localStorage.getItem("adminToken");
      console.log("طلب أدمن - التوكن:", token ? "موجود" : "غير موجود");
    } else {
      // طلبات المستخدمين العاديين
      token = localStorage.getItem("token");
    }

    if (token) {
      // التحقق من أن التوكن ليس فارغاً أو null
      if (token.trim() && token !== "null" && token !== "undefined") {
        config.headers.Authorization = `Bearer ${token}`;
        console.log("تم إضافة التوكن للطلب:", config.url);
      } else {
        console.log("توكن غير صالح، إزالة التوكن:", config.url);
        if (config.url && config.url.startsWith("/admin")) {
          localStorage.removeItem("adminToken");
        } else {
          localStorage.removeItem("token");
        }
      }
    } else {
      console.log("لا يوجد توكن للطلب:", config.url);
    }

    // إزالة رأس x-client-info الذي يسبب مشكلة CORS
    if (config.headers["x-client-info"]) {
      console.log("إزالة رأس x-client-info لتجنب مشكلة CORS");
      delete config.headers["x-client-info"];
    }

    return config;
  },
  (error) => {
    console.error("API Request Error:", error);
    return Promise.reject(error);
  }
);

// إضافة معترض للاستجابات للتعامل مع أخطاء المصادقة
api.interceptors.response.use(
  (response) => {
    console.log(
      "استجابة ناجحة من:",
      response.config.url,
      "الحالة:",
      response.status
    );
    return response;
  },
  (error) => {
    console.error(
      "خطأ في الطلب:",
      error.config?.url,
      "الحالة:",
      error.response?.status
    );
    console.error("تفاصيل الخطأ:", error.response?.data);

    // تجاهل أخطاء 500 في الإشعارات
    if (
      error.response &&
      error.response.status === 500 &&
      (error.config.url.includes("/notifications") ||
        error.config.url.includes("/auth/register-firebase-user"))
    ) {
      console.log("تجاهل خطأ 500 في:", error.config.url);
      // إذا كان الخطأ في إنشاء إشعار، نعيد كائن فارغ بدلاً من رمي الخطأ
      if (
        error.config.method === "post" &&
        error.config.url.includes("/notifications")
      ) {
        console.log("إرجاع استجابة وهمية لإنشاء إشعار");
        return Promise.resolve({
          data: { success: true, message: "تم تجاهل خطأ إنشاء الإشعار" },
        });
      }
    }

    if (error.response && error.response.status === 401) {
      // التحقق من نوع الطلب (أدمن أم مستخدم عادي)
      const isAdminRequest =
        error.config.url && error.config.url.startsWith("/admin");

      if (isAdminRequest) {
        // للأدمن: حذف بيانات الأدمن والانتقال لصفحة تسجيل دخول الأدمن
        console.log("خطأ 401 في طلب أدمن، تسجيل خروج الأدمن");
        localStorage.removeItem("adminToken");
        localStorage.removeItem("admin");
        localStorage.removeItem("adminTokenExpiry");

        // تجنب إعادة التوجيه إذا كان المستخدم بالفعل في صفحة تسجيل دخول الأدمن
        if (!window.location.pathname.includes("/admin/login")) {
          window.location.href = "/admin/login";
        }
        return Promise.reject(error);
      }

      // التحقق من وجود requireReauth في الاستجابة
      // لكن تجاهل هذا الخطأ إذا كان في طلبات معينة بعد تسجيل الدخول مباشرة
      if (error.response.data?.requireReauth) {
        const isProfileRequest = error.config.url.includes("/users/me");
        const currentPath = window.location.pathname;

        // تجاهل requireReauth في طلبات الملف الشخصي بعد تسجيل الدخول مباشرة
        if (
          isProfileRequest &&
          (currentPath === "/login" || currentPath === "/")
        ) {
          console.log(
            "تجاهل requireReauth في طلب الملف الشخصي بعد تسجيل الدخول"
          );
          return Promise.reject(error);
        }

        console.log("تم طلب إعادة المصادقة، تسجيل الخروج");
        localStorage.removeItem("token");
        localStorage.removeItem("user");
        localStorage.removeItem("userType");
        localStorage.removeItem("userId");
        localStorage.removeItem("tokenExpiry");
        window.location.href = "/login";
        return Promise.reject(error);
      }

      // تسجيل الخروج إذا انتهت صلاحية الرمز
      // لكن لا نقوم بتسجيل الخروج إذا كانت محاولة تسجيل دخول أو إنشاء حجز أو إشعار أو عند التنقل بين الصفحات
      const ignoredUrls = [
        "/auth/login",
        "/auth/register",
        "/auth/me",
        "/bookings",
        "/notifications",
        "/craftsmen",
        "/reviews",
        "/users/change-password",
        "/users/profile",
        "/settings",
      ];

      // تجاهل خطأ 401 في /auth/me إذا لم يكن هناك توكن
      const isAuthMeRequest = error.config.url.includes("/auth/me");
      const hasToken = localStorage.getItem("token");

      const shouldIgnore =
        ignoredUrls.some((url) => error.config.url.includes(url)) ||
        (isAuthMeRequest && !hasToken);

      if (!shouldIgnore) {
        console.log("تسجيل الخروج بسبب خطأ 401 في:", error.config.url);

        // التحقق من أن التوكن موجود فعلاً قبل الحذف
        if (hasToken) {
          console.log("حذف التوكن المنتهي الصلاحية");
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          localStorage.removeItem("userType");
          localStorage.removeItem("userId");
          localStorage.removeItem("tokenExpiry");
        } else {
          console.log("لا يوجد توكن للحذف");
        }

        // نتحقق إذا كان المستخدم يتصفح صفحة حرفي أو صفحة أخرى مهمة، لا نقوم بإعادة التوجيه
        const currentPath = window.location.pathname;
        const criticalPaths = [
          "/profile/craftsman",
          "/search",
          "/home",
          "/booking",
          "/login",
          "/register",
        ];

        const isOnCriticalPath = criticalPaths.some((path) =>
          currentPath.includes(path)
        );

        if (!isOnCriticalPath) {
          // إعادة التوجيه فقط إذا لم يكن المستخدم على صفحة مهمة
          window.location.href = "/login";
        } else {
          console.log(
            "تجاهل إعادة التوجيه لأن المستخدم على صفحة مهمة:",
            currentPath
          );
        }
      } else {
        console.log("تجاهل خطأ 401 في:", error.config.url);
      }
    }

    // إضافة معلومات إضافية للخطأ
    if (error.response) {
      error.customMessage =
        error.response.data?.message || "حدث خطأ في الاتصال بالخادم";
    } else if (error.request) {
      error.customMessage = "لم يتم استلام استجابة من الخادم";
    } else {
      error.customMessage = "حدث خطأ أثناء إعداد الطلب";
    }

    return Promise.reject(error);
  }
);

// خدمات المصادقة
export const authService = {
  // تسجيل مستخدم جديد
  register: async (userData) => {
    const response = await api.post("/auth/register", userData);
    return response.data;
  },

  // تسجيل الدخول
  login: async (credentials) => {
    const response = await api.post("/auth/login", credentials);
    return response.data;
  },

  // تسجيل دخول المدير
  adminLogin: async (credentials) => {
    const response = await api.post("/auth/admin/login", credentials);
    return response.data;
  },

  // الحصول على بيانات المستخدم الحالي
  getCurrentUser: async () => {
    const response = await api.get("/auth/me");
    return response.data;
  },
};

// خدمات المستخدمين
export const userService = {
  // الحصول على الملف الشخصي
  getProfile: async () => {
    try {
      const response = await api.get("/users/me");
      console.log("تم الحصول على بيانات المستخدم من الخادم:", response.data);
      return response.data;
    } catch (error) {
      console.error("خطأ في الحصول على بيانات المستخدم من الخادم:", error);
      throw error;
    }
  },

  // تحديث الملف الشخصي
  updateProfile: async (userData) => {
    const response = await api.put("/users/me", userData);
    return response.data;
  },

  // تغيير كلمة المرور
  changePassword: async (passwordData) => {
    const response = await api.put("/users/change-password", passwordData);
    return response.data;
  },

  // تحميل صورة الملف الشخصي
  uploadProfileImage: async (formData) => {
    const response = await api.post("/users/upload-profile-image", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },
};

// خدمات الأدمن
export const adminService = {
  // تسجيل دخول الأدمن
  login: async (credentials) => {
    const response = await api.post("/admin/login", credentials);
    return response.data;
  },

  // الحصول على بيانات الأدمن
  getProfile: async () => {
    const response = await api.get("/admin/profile");
    return response.data;
  },

  // تحديث بيانات الأدمن
  updateProfile: async (profileData) => {
    const response = await api.put("/admin/profile", profileData);
    return response.data;
  },

  // تحديث كلمة مرور الأدمن
  updatePassword: async (passwordData) => {
    const response = await api.put("/admin/password", passwordData);
    return response.data;
  },

  // رفع صورة الأدمن
  uploadImage: async (imageFile) => {
    const formData = new FormData();
    formData.append("image", imageFile);
    const response = await api.post("/admin/upload-image", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  // إحصائيات لوحة التحكم
  getDashboardStats: async () => {
    const response = await api.get("/admin/stats");
    return response.data;
  },

  // إدارة المستخدمين
  getAllUsers: async () => {
    const response = await api.get("/admin/users");
    return response.data;
  },

  // تحديث مستخدم (أدمن)
  updateUser: async (userId, userData) => {
    const response = await api.put(`/admin/users/${userId}`, userData);
    return response.data;
  },

  // حذف مستخدم
  deleteUser: async (userId) => {
    const response = await api.delete(`/admin/users/${userId}`);
    return response.data;
  },

  // إدارة الحرفيين
  getAllCraftsmen: async () => {
    const response = await api.get("/admin/craftsmen");
    return response.data;
  },

  // تحديث حرفي
  updateCraftsman: async (craftsmanId, craftsmanData) => {
    const response = await api.put(
      `/admin/craftsmen/${craftsmanId}`,
      craftsmanData
    );
    return response.data;
  },

  // حذف حرفي
  deleteCraftsman: async (craftsmanId) => {
    const response = await api.delete(`/admin/craftsmen/${craftsmanId}`);
    return response.data;
  },

  // إدارة الحجوزات
  getAllBookings: async () => {
    const response = await api.get("/admin/bookings");
    return response.data;
  },

  // تحديث حالة الحجز
  updateBookingStatus: async (bookingId, status) => {
    const response = await api.put(`/admin/bookings/${bookingId}/status`, {
      status,
    });
    return response.data;
  },

  // معالجة الحجوزات المنتهية الصلاحية
  processExpiredBookings: async () => {
    const response = await api.post("/admin/bookings/process-expired");
    return response.data;
  },
};

// خدمات الحرفيين
export const craftsmanService = {
  // الحصول على جميع الحرفيين
  getAllCraftsmen: async () => {
    const response = await api.get("/craftsmen");
    return response.data;
  },

  // الحصول على حرفي بواسطة المعرف
  getCraftsmanById: async (id) => {
    const response = await api.get(`/craftsmen/${id}`);
    return response.data;
  },

  // الحصول على الشوارع ضمن نطاق عمل الحرفي
  getStreetsInWorkRange: async (id) => {
    const response = await api.get(`/craftsmen/${id}/streets`);
    return response.data;
  },

  // البحث عن الحرفيين
  searchCraftsmen: async (filters) => {
    const response = await api.post("/craftsmen/search", filters);
    return response.data;
  },

  // البحث عن الحرفيين بواسطة الشارع
  searchCraftsmenByStreet: async (street, filters = {}) => {
    const response = await api.post("/craftsmen/search", {
      ...filters,
      street,
    });
    return response.data;
  },

  // البحث عن الحرفيين بواسطة الحي
  searchCraftsmenByNeighborhood: async (neighborhood, filters = {}) => {
    const response = await api.post("/craftsmen/search", {
      ...filters,
      neighborhood,
    });
    return response.data;
  },

  // الحصول على الملف الشخصي للحرفي الحالي
  getMyProfile: async () => {
    const response = await api.get("/craftsmen/me/profile");
    return response.data;
  },

  // تحديث الملف الشخصي للحرفي
  updateProfile: async (profileData) => {
    // إضافة الشوارع والمساجد والمستشفيات إلى بيانات الحرفي إذا كانت موجودة
    const dataToSend = { ...profileData };

    // طباعة البيانات للتصحيح
    console.log("بيانات الحرفي المرسلة للتحديث:", dataToSend);

    const response = await api.put("/craftsmen/me/profile", dataToSend);
    return response.data;
  },

  // تحديث معرض الأعمال
  updateGallery: async (galleryData) => {
    try {
      console.log("updateGallery - بدء تحديث معرض الأعمال:", {
        galleryDataLength: galleryData ? galleryData.length : 0,
        galleryData: galleryData,
      });

      // تصفية أي بيانات فارغة أو غير صالحة
      const validGallery = Array.isArray(galleryData)
        ? galleryData.filter((item) => {
            // إذا كان العنصر نص (URL)
            if (typeof item === "string") {
              return item && item !== "undefined" && item !== "null";
            }
            // إذا كان العنصر كائن
            if (typeof item === "object" && item !== null) {
              return (
                item.url && item.url !== "undefined" && item.url !== "null"
              );
            }
            return false;
          })
        : [];

      console.log("updateGallery - معرض الصور بعد التصفية:", {
        original: galleryData ? galleryData.length : 0,
        filtered: validGallery.length,
        validGallery: validGallery,
      });

      const response = await api.put("/craftsmen/me/gallery", {
        workGallery: validGallery,
      });

      console.log("updateGallery - استجابة تحديث معرض الأعمال:", response.data);
      return response.data;
    } catch (error) {
      console.error("updateGallery - خطأ في تحديث معرض الأعمال:", error);
      throw error;
    }
  },

  // الحصول على معرض أعمال الحرفي
  getCraftsmanGallery: async (craftsmanId) => {
    console.log("getCraftsmanGallery - طلب معرض الصور للحرفي:", craftsmanId);

    // استخدام مسار me/gallery مباشرة لتجنب مشاكل المسارات
    try {
      // إذا تم تمرير معرف الحرفي وهو ليس معرف المستخدم الحالي
      if (craftsmanId && craftsmanId !== "me") {
        console.log(
          "getCraftsmanGallery - استخدام معرف الحرفي المحدد:",
          craftsmanId
        );
        try {
          const response = await api.get(`/craftsmen/${craftsmanId}/gallery`);
          console.log(
            "getCraftsmanGallery - تم استلام البيانات بنجاح:",
            response.data
          );
          return response.data;
        } catch (error) {
          console.error(
            "getCraftsmanGallery - خطأ في الحصول على معرض الصور باستخدام معرف الحرفي:",
            error
          );
          // إرجاع مصفوفة فارغة بدلاً من رمي خطأ
          return { gallery: [], workGallery: [] };
        }
      }

      // محاولة الحصول على معرض الصور للمستخدم الحالي
      console.log(
        "getCraftsmanGallery - محاولة الحصول على معرض الصور للمستخدم الحالي"
      );
      const meResponse = await api.get(`/craftsmen/me/gallery`);
      console.log(
        "getCraftsmanGallery - تم استلام بيانات المستخدم الحالي بنجاح:",
        meResponse.data
      );
      return meResponse.data;
    } catch (meError) {
      console.error(
        "getCraftsmanGallery - خطأ في الحصول على معرض الصور للمستخدم الحالي:",
        meError
      );

      // إذا فشل الطلب وتم تمرير معرف الحرفي، نحاول استخدامه
      if (craftsmanId && craftsmanId !== "me") {
        console.log(
          "getCraftsmanGallery - محاولة ثانية باستخدام معرف الحرفي:",
          craftsmanId
        );
        try {
          const response = await api.get(`/craftsmen/${craftsmanId}/gallery`);
          console.log(
            "getCraftsmanGallery - تم استلام البيانات بنجاح في المحاولة الثانية:",
            response.data
          );
          return response.data;
        } catch (error) {
          console.error(
            "getCraftsmanGallery - خطأ في المحاولة الثانية:",
            error
          );
          // إرجاع مصفوفة فارغة بدلاً من رمي خطأ
          return { gallery: [], workGallery: [] };
        }
      }

      // إرجاع مصفوفة فارغة بدلاً من رمي خطأ
      console.log("getCraftsmanGallery - إرجاع مصفوفة فارغة");
      return { gallery: [], workGallery: [] };
    }
  },

  // تحديث حالة التوفر
  updateAvailability: async (available) => {
    const response = await api.put("/craftsmen/me/availability", { available });
    return response.data;
  },

  // تحديث الشوارع ضمن نطاق العمل
  updateStreetsInWorkRange: async () => {
    const response = await api.put("/craftsmen/me/streets");
    return response.data;
  },

  // تحميل صور لمعرض الأعمال
  uploadGalleryImages: async (formData) => {
    const response = await api.post("/craftsmen/me/upload-gallery", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },

  // تحديث ساعات العمل
  updateWorkingHours: async (workingHoursData) => {
    try {
      console.log(
        "updateWorkingHours - بدء تحديث ساعات العمل:",
        workingHoursData
      );
      // تحويل كائن ساعات العمل إلى مصفوفة
      const workingHoursArray = Object.entries(workingHoursData).map(
        ([day, data]) => ({
          day,
          isWorking: data.isWorking || false,
          start: data.start || "",
          end: data.end || "",
        })
      );

      // إرسال كل من workingHours و workingHoursArray إلى الخادم
      const response = await api.put("/working-hours", {
        workingHours: workingHoursData,
        workingHoursArray: workingHoursArray,
      });
      console.log(
        "updateWorkingHours - استجابة تحديث ساعات العمل:",
        response.data
      );

      // تحديث ساعات العمل في localStorage
      try {
        const user = JSON.parse(localStorage.getItem("user") || "{}");
        user.workingHours = workingHoursData;

        // استخدام مصفوفة ساعات العمل التي تم إنشاؤها سابقاً
        // تحديث مصفوفة ساعات العمل أيضاً
        user.workingHoursArray = workingHoursArray;

        localStorage.setItem("user", JSON.stringify(user));
        console.log(
          "updateWorkingHours - تم تحديث ساعات العمل في localStorage:",
          workingHoursData
        );
        console.log(
          "updateWorkingHours - تم تحديث مصفوفة ساعات العمل في localStorage:",
          workingHoursArray
        );
      } catch (localStorageError) {
        console.error(
          "updateWorkingHours - خطأ في تحديث ساعات العمل في localStorage:",
          localStorageError
        );
      }

      // تأكد من إرجاع workingHoursArray مع الاستجابة
      const responseData = response.data;

      // إذا لم تكن workingHoursArray موجودة في الاستجابة، نضيفها
      if (!responseData.workingHoursArray) {
        // تحويل كائن ساعات العمل إلى مصفوفة
        const workingHoursArray = Object.entries(workingHoursData).map(
          ([day, data]) => ({
            day,
            isWorking: data.isWorking || false,
            start: data.start || "",
            end: data.end || "",
          })
        );

        // إضافة مصفوفة ساعات العمل إلى الاستجابة
        responseData.workingHoursArray = workingHoursArray;
      }

      return responseData;
    } catch (error) {
      console.error("updateWorkingHours - خطأ في تحديث ساعات العمل:", error);

      // في حالة فشل الاتصال بالخادم، نقوم بتحديث ساعات العمل في localStorage فقط
      try {
        const user = JSON.parse(localStorage.getItem("user") || "{}");
        user.workingHours = workingHoursData;

        // تحويل كائن ساعات العمل إلى مصفوفة
        const workingHoursArray = Object.entries(workingHoursData).map(
          ([day, data]) => ({
            day,
            isWorking: data.isWorking || false,
            start: data.start || "",
            end: data.end || "",
          })
        );

        // استخدام مصفوفة ساعات العمل التي تم إنشاؤها سابقاً
        // تحديث مصفوفة ساعات العمل أيضاً
        user.workingHoursArray = workingHoursArray;

        localStorage.setItem("user", JSON.stringify(user));
        console.log(
          "updateWorkingHours - تم تحديث ساعات العمل في localStorage فقط:",
          workingHoursData
        );
        console.log(
          "updateWorkingHours - تم تحديث مصفوفة ساعات العمل في localStorage فقط:",
          user.workingHoursArray
        );

        // محاولة تحديث بيانات الحرفي مباشرة
        try {
          console.log("updateWorkingHours - محاولة تحديث بيانات الحرفي مباشرة");

          // الحصول على معرف المستخدم
          const userId = user.id || user._id;
          if (userId) {
            // تحديث بيانات الحرفي مباشرة
            const craftsmanUpdateResponse = await api.put(
              `/craftsmen/${userId}`,
              {
                workingHoursArray: user.workingHoursArray,
              }
            );

            console.log(
              "updateWorkingHours - تم تحديث بيانات الحرفي مباشرة:",
              craftsmanUpdateResponse.data
            );

            return {
              message: "تم تحديث ساعات العمل بنجاح (تحديث مباشر)",
              workingHours: workingHoursData,
              workingHoursArray: user.workingHoursArray,
              craftsmanUpdateResponse: craftsmanUpdateResponse.data,
            };
          }
        } catch (craftsmanUpdateError) {
          console.error(
            "updateWorkingHours - خطأ في تحديث بيانات الحرفي مباشرة:",
            craftsmanUpdateError
          );
        }

        // إرجاع استجابة وهمية في حالة فشل كل المحاولات
        return {
          message: "تم تحديث ساعات العمل بنجاح (وضع الطوارئ)",
          workingHours: workingHoursData,
          workingHoursArray: user.workingHoursArray,
        };
      } catch (localStorageError) {
        console.error(
          "updateWorkingHours - خطأ في تحديث ساعات العمل في localStorage:",
          localStorageError
        );
      }

      throw error;
    }
  },

  // الحصول على ساعات العمل
  getWorkingHours: async () => {
    try {
      console.log("getWorkingHours - طلب ساعات العمل");
      const response = await api.get("/working-hours");
      console.log("getWorkingHours - استجابة ساعات العمل:", response.data);

      // تأكد من إرجاع workingHoursArray مع الاستجابة
      const responseData = response.data;

      // إذا لم تكن workingHoursArray موجودة في الاستجابة، نضيفها
      if (responseData.workingHours && !responseData.workingHoursArray) {
        // تحويل كائن ساعات العمل إلى مصفوفة
        const workingHoursArray = Object.entries(responseData.workingHours).map(
          ([day, data]) => ({
            day,
            isWorking: data.isWorking || false,
            start: data.start || "",
            end: data.end || "",
          })
        );

        // إضافة مصفوفة ساعات العمل إلى الاستجابة
        responseData.workingHoursArray = workingHoursArray;
        console.log(
          "getWorkingHours - تمت إضافة مصفوفة ساعات العمل إلى الاستجابة:",
          workingHoursArray
        );
      }

      return responseData;
    } catch (error) {
      console.error("getWorkingHours - خطأ في الحصول على ساعات العمل:", error);

      // محاولة استرجاع ساعات العمل من localStorage كحل بديل
      try {
        const user = JSON.parse(localStorage.getItem("user") || "{}");

        // محاولة استخدام workingHours من localStorage
        if (
          user &&
          user.workingHours &&
          Object.keys(user.workingHours).length > 0
        ) {
          console.log(
            "getWorkingHours - استخدام ساعات العمل من localStorage:",
            user.workingHours
          );
          return {
            workingHours: user.workingHours,
            workingHoursArray:
              user.workingHoursArray ||
              Object.entries(user.workingHours).map(([day, data]) => ({
                day,
                isWorking: data.isWorking || false,
                start: data.start || "",
                end: data.end || "",
              })),
          };
        }

        // محاولة استخدام workingHoursArray من localStorage وتحويلها إلى workingHours
        if (
          user &&
          user.workingHoursArray &&
          Array.isArray(user.workingHoursArray) &&
          user.workingHoursArray.length > 0
        ) {
          console.log(
            "getWorkingHours - استخدام مصفوفة ساعات العمل من localStorage:",
            user.workingHoursArray
          );

          // تحويل المصفوفة إلى كائن
          const convertedWorkingHours = {};
          user.workingHoursArray.forEach((dayData) => {
            if (dayData && dayData.day) {
              convertedWorkingHours[dayData.day] = {
                isWorking: dayData.isWorking || false,
                start: dayData.start || "",
                end: dayData.end || "",
              };
            }
          });

          console.log(
            "getWorkingHours - تم تحويل مصفوفة ساعات العمل إلى كائن:",
            convertedWorkingHours
          );
          return {
            workingHours: convertedWorkingHours,
            workingHoursArray: user.workingHoursArray,
          };
        }

        // محاولة استرجاع ساعات العمل من API الحرفي
        try {
          console.log(
            "getWorkingHours - محاولة جلب بيانات الحرفي للحصول على ساعات العمل"
          );

          // محاولة جلب بيانات الحرفي من الخادم
          let craftsmanResponse;
          try {
            // محاولة استخدام نقطة النهاية /craftsmen/me
            craftsmanResponse = await api.get("/craftsmen/me");
            console.log(
              "getWorkingHours - استجابة بيانات الحرفي من /craftsmen/me:",
              craftsmanResponse.data
            );
          } catch (meError) {
            console.error(
              "getWorkingHours - خطأ في جلب بيانات الحرفي من /craftsmen/me:",
              meError
            );

            // محاولة استخدام نقطة النهاية /craftsmen/{id}
            try {
              const userId = user.id || user._id;
              if (userId) {
                console.log(
                  "getWorkingHours - محاولة جلب بيانات الحرفي من /craftsmen/" +
                    userId
                );
                craftsmanResponse = await api.get(`/craftsmen/${userId}`);
                console.log(
                  "getWorkingHours - استجابة بيانات الحرفي من /craftsmen/" +
                    userId +
                    ":",
                  craftsmanResponse.data
                );
              } else {
                throw new Error("لا يوجد معرف للمستخدم");
              }
            } catch (idError) {
              console.error(
                "getWorkingHours - خطأ في جلب بيانات الحرفي من /craftsmen/{id}:",
                idError
              );
              throw idError; // إعادة رمي الخطأ للتعامل معه في الكود الخارجي
            }
          }

          // محاولة استخدام workingHoursArray من استجابة الحرفي
          if (
            craftsmanResponse.data &&
            craftsmanResponse.data.workingHoursArray &&
            Array.isArray(craftsmanResponse.data.workingHoursArray) &&
            craftsmanResponse.data.workingHoursArray.length > 0
          ) {
            console.log(
              "getWorkingHours - استخدام مصفوفة ساعات العمل من استجابة الحرفي:",
              craftsmanResponse.data.workingHoursArray
            );

            // تحويل المصفوفة إلى كائن
            const convertedWorkingHours = {};
            craftsmanResponse.data.workingHoursArray.forEach((dayData) => {
              if (dayData && dayData.day) {
                convertedWorkingHours[dayData.day] = {
                  isWorking: dayData.isWorking || false,
                  start: dayData.start || "",
                  end: dayData.end || "",
                };
              }
            });

            console.log(
              "getWorkingHours - تم تحويل مصفوفة ساعات العمل من استجابة الحرفي إلى كائن:",
              convertedWorkingHours
            );

            // تحديث ساعات العمل في localStorage
            try {
              const localUser = JSON.parse(
                localStorage.getItem("user") || "{}"
              );
              localUser.workingHours = convertedWorkingHours;
              localStorage.setItem("user", JSON.stringify(localUser));
              console.log(
                "getWorkingHours - تم تحديث ساعات العمل في localStorage:",
                convertedWorkingHours
              );
            } catch (updateError) {
              console.error(
                "getWorkingHours - خطأ في تحديث ساعات العمل في localStorage:",
                updateError
              );
            }

            return {
              workingHours: convertedWorkingHours,
              workingHoursArray: craftsmanResponse.data.workingHoursArray,
            };
          }
        } catch (craftsmanError) {
          console.error(
            "getWorkingHours - خطأ في جلب بيانات الحرفي:",
            craftsmanError
          );
        }
      } catch (localStorageError) {
        console.error(
          "getWorkingHours - خطأ في استرجاع ساعات العمل من localStorage:",
          localStorageError
        );
      }

      // إرجاع كائن فارغ بدلاً من رمي خطأ
      return {
        workingHours: {},
        workingHoursArray: [],
      };
    }
  },
};

// خدمات الحجوزات
export const bookingService = {
  // إنشاء حجز جديد
  createBooking: async (bookingData) => {
    try {
      const response = await api.post("/bookings", bookingData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // الحصول على حجوزات المستخدم الحالي
  getMyBookings: async () => {
    const response = await api.get("/bookings/me");
    return response.data;
  },

  // الحصول على حجز بواسطة المعرف
  getBookingById: async (id) => {
    const response = await api.get(`/bookings/${id}`);
    return response.data;
  },

  // تحديث حالة الحجز
  updateBookingStatus: async (id, status) => {
    try {
      // إرسال الطلب إلى الخادم
      const response = await api.put(`/bookings/${id}/status`, { status });
      return response.data;
    } catch (error) {
      // إذا كان الخطأ 400، نعيد كائن بالحالة الجديدة
      if (error.response && error.response.status === 400) {
        return {
          id,
          status,
          updatedLocally: true,
          message:
            error.response?.data?.message || "تم تحديث الحالة محلياً فقط",
        };
      }

      // إذا كان الخطأ 403 (غير مصرح)، نعيد كائن بالحالة الجديدة مع رسالة خطأ
      if (error.response && error.response.status === 403) {
        return {
          id,
          status,
          updatedLocally: true,
          message: error.response?.data?.message || "غير مصرح بهذا الإجراء",
        };
      }

      throw error;
    }
  },

  // تعديل حجز
  updateBooking: async (id, bookingData) => {
    const response = await api.put(`/bookings/${id}`, bookingData);
    return response.data;
  },

  // تحديث الحجز بمعرف التقييم
  updateBookingWithReview: async (bookingId, reviewId) => {
    try {
      const response = await api.put(`/bookings/${bookingId}/review`, {
        reviewId,
      });
      return response.data;
    } catch (error) {
      throw error;
    }
  },
};

// خدمات التقييمات
export const reviewService = {
  // إنشاء تقييم جديد
  createReview: async (reviewData) => {
    const response = await api.post("/reviews", reviewData);
    return response.data;
  },

  // تحديث تقييم موجود
  updateReview: async (reviewId, reviewData) => {
    try {
      const response = await api.put(`/reviews/${reviewId}`, reviewData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // الحصول على تقييمات حرفي
  getCraftsmanReviews: async (craftsmanId) => {
    const response = await api.get(`/reviews/craftsman/${craftsmanId}`);
    return response.data;
  },

  // الحصول على تقييمات مفصلة لحرفي
  getCraftsmanDetailedRatings: async (craftsmanId) => {
    const response = await api.get(`/reviews/craftsman/${craftsmanId}/ratings`);
    return response.data;
  },

  // الحصول على تقييم بواسطة المعرف
  getReviewById: async (id) => {
    const response = await api.get(`/reviews/${id}`);
    return response.data;
  },

  // الحصول على جميع التقييمات
  getAllReviews: async () => {
    const response = await api.get("/reviews");
    return response.data;
  },

  // تحميل صور للتقييم
  uploadReviewImages: async (formData) => {
    const response = await api.post("/reviews/upload-images", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  },
};

// خدمات المهن
export const professionService = {
  // الحصول على جميع المهن
  getAllProfessions: async () => {
    const response = await api.get("/professions");
    return response.data;
  },

  // الحصول على مهنة بواسطة المعرف
  getProfessionById: async (id) => {
    const response = await api.get(`/professions/${id}`);
    return response.data;
  },

  // إنشاء مهنة جديدة (للإدارة)
  createProfession: async (professionData) => {
    const response = await api.post("/professions", professionData);
    return response.data;
  },

  // تحديث مهنة (للإدارة)
  updateProfession: async (id, professionData) => {
    const response = await api.put(`/professions/${id}`, professionData);
    return response.data;
  },

  // حذف مهنة (للإدارة)
  deleteProfession: async (id) => {
    const response = await api.delete(`/professions/${id}`);
    return response.data;
  },
};

// خدمات الإشعارات
export const notificationService = {
  // الحصول على إشعارات المستخدم الحالي
  getMyNotifications: async () => {
    const response = await api.get("/notifications/me");
    return response.data;
  },

  // إنشاء إشعار جديد
  createNotification: async (notificationData) => {
    const response = await api.post("/notifications", notificationData);
    return response.data;
  },

  // تعليم إشعار كمقروء
  markAsRead: async (id) => {
    const response = await api.put(`/notifications/${id}/read`);
    return response.data;
  },

  // تعليم جميع الإشعارات كمقروءة
  markAllAsRead: async () => {
    const response = await api.put("/notifications/read-all");
    return response.data;
  },

  // حذف إشعار
  deleteNotification: async (id) => {
    const response = await api.delete(`/notifications/${id}`);
    return response.data;
  },

  // حذف جميع الإشعارات
  deleteAllNotifications: async () => {
    const response = await api.delete("/notifications");
    return response.data;
  },
};

// خدمات معرض الأعمال
export const workGalleryService = {
  // الحصول على معرض أعمال حرفي محدد
  getCraftsmanGallery: async (craftsmanId) => {
    const response = await api.get(`/work-gallery/craftsman/${craftsmanId}`);
    return response.data;
  },

  // الحصول على معرض أعمال الحرفي الحالي
  getMyGallery: async () => {
    const response = await api.get("/work-gallery/my-gallery");
    return response.data;
  },

  // إضافة صور إلى معرض الأعمال
  addImages: async (images) => {
    const response = await api.post("/work-gallery/add", { images });
    return response.data;
  },

  // حذف صورة من معرض الأعمال
  removeImage: async (imageId, imageUrl) => {
    const response = await api.delete("/work-gallery/remove", {
      data: { imageId, imageUrl },
    });
    return response.data;
  },

  // تحديث ترتيب الصور
  reorderImages: async (orderedImages) => {
    const response = await api.put("/work-gallery/reorder", { orderedImages });
    return response.data;
  },

  // مسح المعرض بالكامل
  clearGallery: async () => {
    const response = await api.delete("/work-gallery/clear");
    return response.data;
  },
};

export default api;
