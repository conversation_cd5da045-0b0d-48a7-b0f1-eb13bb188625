import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import Layout from "../../components/Layout/Layout";
import Input from "../../components/UI/Input";
import Button from "../../components/UI/Button";
import LoadingButton from "../../components/UI/LoadingButton";
import CountryCodeSelectButtons from "../../components/UI/CountryCodeSelectButtons";
import { useTheme } from "../../contexts/ThemeContext";
import authService from "../../services/authService";
import toastUtils from "../../utils/toastUtils";

const ForgotPasswordPage = () => {
  const [step, setStep] = useState(1); // 1: phone, 2: otp, 3: new password
  const [formData, setFormData] = useState({
    phone: "",
    countryCode: "+963",
    otp: "",
    newPassword: "",
    confirmNewPassword: "",
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);

  const { darkMode } = useTheme();
  const navigate = useNavigate();

  // عداد إعادة الإرسال
  useEffect(() => {
    let timer;
    if (resendCooldown > 0) {
      timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [resendCooldown]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  const validatePhone = () => {
    const newErrors = {};
    
    if (!formData.phone.trim()) {
      newErrors.phone = "رقم الهاتف مطلوب";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validateOtp = () => {
    const newErrors = {};
    
    if (!formData.otp.trim()) {
      newErrors.otp = "رمز التحقق مطلوب";
    } else if (formData.otp.length !== 6) {
      newErrors.otp = "رمز التحقق يجب أن يكون 6 أرقام";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const validatePassword = () => {
    const newErrors = {};
    
    if (!formData.newPassword) {
      newErrors.newPassword = "كلمة المرور الجديدة مطلوبة";
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = "كلمة المرور يجب أن تكون 6 أحرف على الأقل";
    }
    
    if (formData.newPassword !== formData.confirmNewPassword) {
      newErrors.confirmNewPassword = "كلمات المرور غير متطابقة";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (step === 1) {
      if (!validatePhone()) return;
      
      setIsLoading(true);
      try {
        const cleanedPhone = formData.phone.replace(/\D/g, "").replace(/^0+/, "");
        const fullPhoneNumber = formData.countryCode + cleanedPhone;
        
        // التحقق من وجود الحساب
        const phoneCheckResponse = await authService.checkPhoneExists(fullPhoneNumber);
        if (!phoneCheckResponse.exists) {
          setErrors({ phone: "رقم الهاتف غير مسجل في النظام" });
          setIsLoading(false);
          return;
        }
        
        // إرسال OTP (محاكاة)
        toastUtils.showToast("تم إرسال رمز التحقق عبر WhatsApp", "success", 3000);
        setStep(2);
        setResendCooldown(60);
        
      } catch (error) {
        toastUtils.showToast("حدث خطأ أثناء إرسال رمز التحقق", "error", 3000);
      } finally {
        setIsLoading(false);
      }
    } else if (step === 2) {
      if (!validateOtp()) return;
      
      // التحقق من OTP (محاكاة - في الواقع يجب التحقق من الخادم)
      if (formData.otp === "123456") {
        setStep(3);
      } else {
        setErrors({ otp: "رمز التحقق غير صحيح" });
      }
    } else if (step === 3) {
      if (!validatePassword()) return;
      
      setIsLoading(true);
      try {
        const cleanedPhone = formData.phone.replace(/\D/g, "").replace(/^0+/, "");
        const fullPhoneNumber = formData.countryCode + cleanedPhone;
        
        await authService.resetPassword(fullPhoneNumber, formData.newPassword);
        toastUtils.showToast("تم تغيير كلمة المرور بنجاح", "success", 3000);
        
        // إعادة توجيه إلى صفحة تسجيل الدخول
        setTimeout(() => {
          navigate("/login");
        }, 2000);
        
      } catch (error) {
        toastUtils.showToast("حدث خطأ أثناء تغيير كلمة المرور", "error", 3000);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleResendOtp = async () => {
    if (resendCooldown > 0) return;
    
    try {
      // إعادة إرسال OTP (محاكاة)
      toastUtils.showToast("تم إعادة إرسال رمز التحقق", "success", 3000);
      setResendCooldown(60);
    } catch (error) {
      toastUtils.showToast("حدث خطأ أثناء إعادة الإرسال", "error", 3000);
    }
  };

  const getStepTitle = () => {
    switch (step) {
      case 1:
        return "استعادة كلمة المرور";
      case 2:
        return "تأكيد رقم الهاتف";
      case 3:
        return "كلمة المرور الجديدة";
      default:
        return "استعادة كلمة المرور";
    }
  };

  const getStepDescription = () => {
    switch (step) {
      case 1:
        return "أدخل رقم الهاتف المرتبط بحسابك";
      case 2:
        return `تم إرسال رمز التحقق إلى ${formData.countryCode}${formData.phone}`;
      case 3:
        return "أدخل كلمة المرور الجديدة";
      default:
        return "";
    }
  };

  return (
    <Layout hideFooter>
      <div className={`min-h-screen py-12 ${darkMode ? "bg-gray-900" : "bg-gradient-to-br from-blue-50 to-indigo-100"} transition-colors duration-300`}>
        <div className="container mx-auto px-4">
          <motion.div
            className={`max-w-lg mx-auto rounded-lg shadow-xl overflow-hidden border-2 ${darkMode ? "bg-gray-800 border-gray-700" : "bg-white border-indigo-200"} transition-colors duration-300`}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <div className="p-6">
              {/* عنوان الصفحة */}
              <h2 className={`text-2xl font-bold text-center mb-2 ${darkMode ? "text-indigo-300" : "text-indigo-800"} relative transition-colors duration-300`}>
                <span className="relative z-10">{getStepTitle()}</span>
                <span className={`absolute bottom-0 left-0 right-0 h-3 ${darkMode ? "bg-indigo-500" : "bg-indigo-300"} opacity-40 transform -rotate-1 z-0`}></span>
              </h2>

              <div className="mb-6 text-center">
                <p className={`${darkMode ? "text-gray-300" : "text-gray-600"} text-sm`}>
                  {getStepDescription()}
                </p>
              </div>

              {/* مؤشر التقدم */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  {[1, 2, 3].map((stepNumber) => (
                    <div
                      key={stepNumber}
                      className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                        stepNumber <= step
                          ? darkMode
                            ? "bg-indigo-600 text-white"
                            : "bg-indigo-600 text-white"
                          : darkMode
                          ? "bg-gray-600 text-gray-300"
                          : "bg-gray-200 text-gray-500"
                      } transition-colors duration-300`}
                    >
                      {stepNumber}
                    </div>
                  ))}
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-indigo-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(step / 3) * 100}%` }}
                  ></div>
                </div>
              </div>

              <form onSubmit={handleSubmit}>
                {step === 1 && (
                  <div className="space-y-4">
                    <div>
                      <label className={`block font-medium mb-2 ${darkMode ? "text-gray-200" : "text-indigo-700"}`}>
                        رقم الهاتف
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        <div className="col-span-2">
                          <input
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            placeholder="أدخل رقم الهاتف"
                            required
                            className="w-full py-2 px-3 border border-indigo-200 rounded-md focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                            dir="ltr"
                          />
                        </div>
                        <div>
                          <CountryCodeSelectButtons
                            value={formData.countryCode}
                            onChange={(code) => setFormData(prev => ({ ...prev, countryCode: code }))}
                          />
                        </div>
                      </div>
                      {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
                    </div>
                  </div>
                )}

                {step === 2 && (
                  <div className="space-y-4">
                    <Input
                      label="رمز التحقق"
                      type="text"
                      name="otp"
                      value={formData.otp}
                      onChange={handleInputChange}
                      placeholder="أدخل رمز التحقق المكون من 6 أرقام"
                      error={errors.otp}
                      required
                      maxLength={6}
                      className="text-center text-lg tracking-widest"
                      dir="ltr"
                    />

                    <div className="text-center">
                      <button
                        type="button"
                        onClick={handleResendOtp}
                        disabled={resendCooldown > 0}
                        className={`text-sm ${
                          resendCooldown > 0
                            ? "text-gray-400 cursor-not-allowed"
                            : darkMode
                            ? "text-indigo-400 hover:text-indigo-300"
                            : "text-indigo-600 hover:text-indigo-800"
                        } transition-colors duration-200`}
                      >
                        {resendCooldown > 0
                          ? `إعادة الإرسال خلال ${resendCooldown} ثانية`
                          : "إعادة إرسال رمز التحقق"}
                      </button>
                    </div>
                  </div>
                )}

                {step === 3 && (
                  <div className="space-y-4">
                    <Input
                      label="كلمة المرور الجديدة"
                      type="password"
                      name="newPassword"
                      value={formData.newPassword}
                      onChange={handleInputChange}
                      placeholder="أدخل كلمة المرور الجديدة"
                      error={errors.newPassword}
                      required
                    />

                    <Input
                      label="تأكيد كلمة المرور الجديدة"
                      type="password"
                      name="confirmNewPassword"
                      value={formData.confirmNewPassword}
                      onChange={handleInputChange}
                      placeholder="أعد إدخال كلمة المرور الجديدة"
                      error={errors.confirmNewPassword}
                      required
                    />
                  </div>
                )}

                <LoadingButton
                  type="submit"
                  variant="primary"
                  fullWidth
                  isLoading={isLoading}
                  loadingText={
                    step === 1 ? "جاري الإرسال..." :
                    step === 2 ? "جاري التحقق..." :
                    "جاري التحديث..."
                  }
                  className="mt-6 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600"
                >
                  {step === 1 && "إرسال رمز التحقق"}
                  {step === 2 && "تأكيد الرمز"}
                  {step === 3 && "تحديث كلمة المرور"}
                </LoadingButton>

                {step > 1 && (
                  <Button
                    type="button"
                    variant="secondary"
                    fullWidth
                    onClick={() => setStep(step - 1)}
                    className="mt-3"
                  >
                    العودة للخطوة السابقة
                  </Button>
                )}
              </form>

              <div className="mt-6 text-center">
                <p className={`${darkMode ? "text-gray-300" : "text-gray-600"} text-sm mb-3`}>
                  تذكرت كلمة المرور؟{" "}
                  <Link
                    to="/login"
                    className={`${darkMode ? "text-indigo-400" : "text-indigo-600"} font-medium hover:text-indigo-800 transition-colors duration-200`}
                  >
                    تسجيل الدخول
                  </Link>
                </p>
              </div>

              <div className="mt-4 text-center">
                <Link
                  to="/"
                  className="text-indigo-500 text-sm hover:text-indigo-700 transition-colors duration-200"
                >
                  العودة إلى الصفحة الرئيسية
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default ForgotPasswordPage;
