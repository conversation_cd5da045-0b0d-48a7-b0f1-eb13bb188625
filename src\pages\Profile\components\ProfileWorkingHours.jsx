import React, { useEffect, useState } from "react";
import Card from "../../../components/common/Card";
import { Clock, Calendar, AlertCircle, Save } from "lucide-react";

const ProfileWorkingHours = ({
  workingHours,
  workingHoursTime,
  isEditing,
  onWorkingHoursChange,
  onWorkingHoursTimeChange,
  darkMode,
  onSaveWorkingHours,
}) => {
  // حالة لتتبع ما إذا كانت هناك تغييرات غير محفوظة
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  // تعريف أيام الأسبوع بالعربية
  const daysMap = {
    saturday: "السبت",
    sunday: "الأحد",
    monday: "الاثنين",
    tuesday: "الثلاثاء",
    wednesday: "الأربعاء",
    thursday: "الخميس",
    friday: "الجمعة",
  };

  // ترتيب أيام الأسبوع للعرض
  const orderedDays = [
    ["saturday", daysMap.saturday],
    ["sunday", daysMap.sunday],
    ["monday", daysMap.monday],
    ["tuesday", daysMap.tuesday],
    ["wednesday", daysMap.wednesday],
    ["thursday", daysMap.thursday],
    ["friday", daysMap.friday],
  ];

  // تحديث أوقات العمل الموحدة عند تحميل المكون
  useEffect(() => {
    // طباعة ساعات العمل في ProfileWorkingHours
    console.log("ساعات العمل في ProfileWorkingHours:", workingHours);

    // البحث عن أول يوم عمل للحصول على ساعات العمل
    const workingDays = Object.entries(workingHours || {})
      .filter(([_, day]) => !!day?.isWorking)
      .map(([dayKey, _]) => dayKey);

    if (workingDays.length > 0) {
      const firstWorkingDay = workingDays[0];
      const firstDayHours = workingHours[firstWorkingDay];

      if (firstDayHours && onWorkingHoursTimeChange) {
        onWorkingHoursTimeChange({
          start: firstDayHours.start || "",
          end: firstDayHours.end || "",
        });

        console.log("تم تحديث وقت العمل الموحد في ProfileWorkingHours:", {
          day: firstWorkingDay,
          start: firstDayHours.start,
          end: firstDayHours.end,
        });
      }
    }
  }, [workingHours, onWorkingHoursTimeChange]);

  // دالة لتحديث حالة يوم العمل (عامل/غير عامل)
  const handleDayToggle = (day) => {
    if (!onWorkingHoursChange) return;

    // التأكد من أن قيمة isWorking هي قيمة منطقية (boolean)
    const currentIsWorking = !!workingHours[day]?.isWorking;

    const updatedWorkingHours = {
      ...workingHours,
      [day]: {
        ...workingHours[day],
        isWorking: !currentIsWorking,
        start: workingHoursTime.start,
        end: workingHoursTime.end,
      },
    };

    console.log(
      `تغيير حالة يوم ${day} من ${currentIsWorking} إلى ${!currentIsWorking}`
    );

    onWorkingHoursChange(updatedWorkingHours);
    setHasUnsavedChanges(true);
  };

  // دالة لحفظ ساعات العمل
  const handleSaveWorkingHours = () => {
    if (onSaveWorkingHours) {
      onSaveWorkingHours();
      setHasUnsavedChanges(false);
    }
  };

  // دالة لتحديث ساعات العمل الموحدة لجميع أيام العمل
  const handleUniformTimeChange = (field, value) => {
    if (!onWorkingHoursChange || !onWorkingHoursTimeChange) return;

    console.log(`تغيير وقت العمل الموحد: ${field} = ${value}`);

    // تحديث حالة أوقات العمل الموحدة
    onWorkingHoursTimeChange({
      ...workingHoursTime,
      [field]: value,
    });

    // تحديث جميع أيام العمل بالوقت الجديد
    const updatedWorkingHours = { ...workingHours };

    Object.keys(updatedWorkingHours).forEach((day) => {
      if (!!updatedWorkingHours[day]?.isWorking) {
        updatedWorkingHours[day] = {
          ...updatedWorkingHours[day],
          [field]: value,
        };
        console.log(`تحديث وقت العمل ليوم ${day}: ${field} = ${value}`);
      }
    });

    // طباعة حالة أيام العمل للتصحيح
    const workingDays = Object.entries(updatedWorkingHours)
      .filter(([_, hours]) => !!hours.isWorking)
      .map(([day, _]) => day);

    console.log("أيام العمل بعد تحديث الوقت:", workingDays);
    console.log("ساعات العمل بعد التحديث:", updatedWorkingHours);

    onWorkingHoursChange(updatedWorkingHours);
    setHasUnsavedChanges(true);
  };

  return (
    <Card
      className={`p-6 mb-6 rounded-xl shadow-lg ${
        darkMode
          ? "bg-gray-800 text-gray-200 border border-gray-700"
          : "bg-gradient-to-br from-blue-50/80 to-indigo-50 border border-indigo-100"
      } transition-colors duration-300 hover:shadow-xl`}
    >
      <div className="flex items-center mb-4">
        <div
          className={`p-2 rounded-full mr-3 ${
            darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
          }`}
        >
          <Clock
            size={24}
            className={`${
              darkMode ? "text-indigo-400" : "text-indigo-500"
            } transition-colors duration-300`}
          />
        </div>
        <h2
          className={`text-xl font-bold ${
            darkMode ? "text-indigo-300" : "text-indigo-800"
          } relative inline-block transition-colors duration-300`}
        >
          <span className="relative z-10">أوقات الدوام</span>
          <span
            className={`absolute bottom-0 left-0 right-0 h-2 ${
              darkMode ? "bg-indigo-600" : "bg-indigo-400"
            } opacity-40 transform -rotate-1 z-0 rounded-full`}
          ></span>
        </h2>
      </div>

      {isEditing ? (
        <div className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* قسم أيام العمل */}
            <div
              className={`p-5 rounded-xl shadow-md ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600/50"
                  : "bg-white/90 border border-indigo-100"
              } transition-all duration-300 hover:shadow-lg`}
            >
              <div className="flex justify-between items-center mb-4">
                <div className="flex items-center">
                  <div
                    className={`p-1.5 rounded-full mr-2 ${
                      darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                    }`}
                  >
                    <Calendar
                      size={18}
                      className={`${
                        darkMode ? "text-indigo-400" : "text-indigo-500"
                      } transition-colors duration-300`}
                    />
                  </div>
                  <h4
                    className={`font-bold ${
                      darkMode ? "text-indigo-300" : "text-indigo-700"
                    }`}
                  >
                    أيام العمل
                  </h4>
                </div>

                <div className="flex gap-2">
                  <button
                    type="button"
                    onClick={() => {
                      // تحديد جميع الأيام
                      const updatedWorkingHours = { ...workingHours };
                      Object.keys(daysMap).forEach((day) => {
                        updatedWorkingHours[day] = {
                          ...updatedWorkingHours[day],
                          isWorking: true, // تعيين قيمة منطقية صريحة
                          start: workingHoursTime.start,
                          end: workingHoursTime.end,
                        };
                      });

                      console.log("تحديد جميع الأيام:", updatedWorkingHours);

                      onWorkingHoursChange(updatedWorkingHours);
                      setHasUnsavedChanges(true);
                    }}
                    className={`text-xs px-3 py-1.5 rounded-lg shadow-sm hover:shadow-md ${
                      darkMode
                        ? "bg-indigo-700 text-white hover:bg-indigo-600"
                        : "bg-indigo-100 text-indigo-700 hover:bg-indigo-200"
                    } transition-all duration-300 font-medium`}
                  >
                    تحديد الكل
                  </button>

                  <button
                    type="button"
                    onClick={() => {
                      // إلغاء تحديد جميع الأيام
                      const updatedWorkingHours = { ...workingHours };
                      Object.keys(daysMap).forEach((day) => {
                        updatedWorkingHours[day] = {
                          ...updatedWorkingHours[day],
                          isWorking: false, // تعيين قيمة منطقية صريحة
                          start:
                            updatedWorkingHours[day]?.start ||
                            workingHoursTime.start,
                          end:
                            updatedWorkingHours[day]?.end ||
                            workingHoursTime.end,
                        };
                      });

                      console.log(
                        "إلغاء تحديد جميع الأيام:",
                        updatedWorkingHours
                      );

                      onWorkingHoursChange(updatedWorkingHours);
                      setHasUnsavedChanges(true);
                    }}
                    className={`text-xs px-3 py-1.5 rounded-lg shadow-sm hover:shadow-md ${
                      darkMode
                        ? "bg-gray-600 text-white hover:bg-gray-500"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                    } transition-all duration-300 font-medium`}
                  >
                    إلغاء الكل
                  </button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-2 mt-4">
                {orderedDays.map(([day, dayName]) => (
                  <div
                    key={day}
                    className={`flex items-center p-2 rounded-lg transition-all duration-300 ${
                      workingHours[day]?.isWorking
                        ? darkMode
                          ? "bg-indigo-900/30 border border-indigo-800/50"
                          : "bg-indigo-50/80 border border-indigo-200"
                        : darkMode
                        ? "bg-gray-800/50 border border-gray-700/50"
                        : "bg-gray-50/80 border border-gray-200"
                    }`}
                  >
                    <input
                      type="checkbox"
                      id={`edit-working-${day}`}
                      checked={workingHours[day]?.isWorking || false}
                      onChange={() => handleDayToggle(day)}
                      className={`w-5 h-5 ml-2 rounded transition-all duration-300 ${
                        darkMode
                          ? "accent-indigo-500 bg-gray-700 border-gray-600"
                          : "accent-indigo-600 bg-white border-indigo-300"
                      }`}
                    />
                    <label
                      htmlFor={`edit-working-${day}`}
                      className={`${
                        workingHours[day]?.isWorking
                          ? darkMode
                            ? "text-indigo-300 font-medium"
                            : "text-indigo-700 font-medium"
                          : darkMode
                          ? "text-gray-400"
                          : "text-gray-500"
                      } cursor-pointer transition-colors duration-300`}
                    >
                      {dayName}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* قسم ساعات العمل */}
            <div
              className={`p-5 rounded-xl shadow-md ${
                darkMode
                  ? "bg-gray-700/80 border border-gray-600/50"
                  : "bg-white/90 border border-indigo-100"
              } transition-all duration-300 hover:shadow-lg`}
            >
              <div className="flex items-center mb-4">
                <div
                  className={`p-1.5 rounded-full mr-2 ${
                    darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                  }`}
                >
                  <Clock
                    size={18}
                    className={`${
                      darkMode ? "text-indigo-400" : "text-indigo-500"
                    } transition-colors duration-300`}
                  />
                </div>
                <h4
                  className={`font-bold ${
                    darkMode ? "text-indigo-300" : "text-indigo-700"
                  }`}
                >
                  ساعات العمل
                </h4>
              </div>

              {Object.values(workingHours || {}).some(
                (day) => !!day?.isWorking
              ) ? (
                <div className="mb-3">
                  <div
                    className={`p-4 rounded-lg mb-4 ${
                      darkMode
                        ? "bg-gray-800/70 border border-gray-700"
                        : "bg-indigo-50/70 border border-indigo-100"
                    }`}
                  >
                    <p
                      className={`text-sm mb-3 font-medium ${
                        darkMode ? "text-indigo-300" : "text-indigo-700"
                      }`}
                    >
                      حدد وقت العمل الموحد لجميع الأيام المختارة
                    </p>
                    <div className="flex flex-col sm:flex-row items-center gap-4">
                      <div className="flex items-center">
                        <span
                          className={`ml-2 font-medium ${
                            darkMode ? "text-gray-300" : "text-gray-700"
                          }`}
                        >
                          من
                        </span>
                        <input
                          type="time"
                          value={workingHoursTime.start}
                          onChange={(e) =>
                            handleUniformTimeChange("start", e.target.value)
                          }
                          className={`p-2 rounded-lg border shadow-sm ${
                            darkMode
                              ? "bg-gray-700 border-gray-600 text-gray-200 focus:border-indigo-500"
                              : "bg-white border-indigo-200 focus:border-indigo-500"
                          } focus:ring-2 focus:ring-indigo-500/50 transition-all duration-300`}
                        />
                      </div>
                      <div className="flex items-center">
                        <span
                          className={`ml-2 font-medium ${
                            darkMode ? "text-gray-300" : "text-gray-700"
                          }`}
                        >
                          إلى
                        </span>
                        <input
                          type="time"
                          value={workingHoursTime.end}
                          onChange={(e) =>
                            handleUniformTimeChange("end", e.target.value)
                          }
                          className={`p-2 rounded-lg border shadow-sm ${
                            darkMode
                              ? "bg-gray-700 border-gray-600 text-gray-200 focus:border-indigo-500"
                              : "bg-white border-indigo-200 focus:border-indigo-500"
                          } focus:ring-2 focus:ring-indigo-500/50 transition-all duration-300`}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <h5
                      className={`text-sm font-medium mb-2 ${
                        darkMode ? "text-gray-300" : "text-gray-700"
                      }`}
                    >
                      أيام العمل المختارة:
                    </h5>
                    <div className="flex flex-wrap gap-2">
                      {orderedDays.map(
                        ([day, dayName]) =>
                          !!workingHours[day]?.isWorking && (
                            <span
                              key={`selected-${day}`}
                              className={`px-3 py-1.5 rounded-full text-xs font-medium shadow-sm ${
                                darkMode
                                  ? "bg-indigo-800 text-indigo-200 border border-indigo-700"
                                  : "bg-indigo-100 text-indigo-800 border border-indigo-200"
                              } transition-all duration-300`}
                            >
                              {dayName}
                            </span>
                          )
                      )}
                    </div>
                  </div>

                  {/* زر حفظ ساعات العمل */}
                  {hasUnsavedChanges && (
                    <div className="mt-6 flex justify-center">
                      <button
                        type="button"
                        onClick={handleSaveWorkingHours}
                        className={`flex items-center gap-2 px-4 py-2 rounded-lg shadow-md ${
                          darkMode
                            ? "bg-green-700 text-white hover:bg-green-600"
                            : "bg-green-600 text-white hover:bg-green-500"
                        } transition-all duration-300 font-medium`}
                      >
                        <Save size={18} />
                        <span>حفظ ساعات العمل</span>
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                <div
                  className={`p-6 rounded-lg text-center ${
                    darkMode
                      ? "bg-gray-800/50 border border-gray-700/50"
                      : "bg-gray-50/80 border border-gray-200"
                  } transition-colors duration-300`}
                >
                  <p
                    className={`py-4 ${
                      darkMode ? "text-gray-400" : "text-gray-500"
                    }`}
                  >
                    الرجاء تحديد أيام العمل أولاً
                  </p>
                </div>
              )}
            </div>
          </div>

          <div
            className={`mt-6 p-4 rounded-lg shadow-md ${
              darkMode
                ? "bg-indigo-900/30 border border-indigo-800/50"
                : "bg-indigo-50/80 border border-indigo-200"
            } transition-all duration-300`}
          >
            <div className="flex items-start">
              <div
                className={`p-1.5 rounded-full mt-0.5 ml-2 ${
                  darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                }`}
              >
                <AlertCircle
                  size={16}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } transition-colors duration-300`}
                />
              </div>
              <div>
                <p
                  className={`text-sm font-medium ${
                    darkMode ? "text-indigo-300" : "text-indigo-700"
                  }`}
                >
                  ملاحظة مهمة:
                </p>
                <p
                  className={`text-sm mt-1 ${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  }`}
                >
                  سيتم استخدام أوقات الدوام لتحديد ما إذا كنت متاحاً للعمل في
                  وقت معين، وسيظهر ذلك للعملاء عند البحث عن حرفيين.
                </p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div
          className={`p-5 rounded-lg ${
            darkMode
              ? "bg-gray-700/50 border border-gray-600/50"
              : "bg-white/80 border border-indigo-100"
          } transition-colors duration-300 shadow-inner`}
        >
          <div className="space-y-4">
            {/* عرض أيام العمل */}
            <div className="flex items-center p-3 rounded-lg bg-opacity-50 transition-all duration-300 hover:bg-opacity-70 hover:shadow-sm">
              <div
                className={`p-2 rounded-full ml-3 ${
                  darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                }`}
              >
                <Calendar
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } transition-colors duration-300`}
                />
              </div>
              <div
                className={`${darkMode ? "text-gray-300" : "text-gray-700"}`}
              >
                <div className="font-bold mb-1">أيام العمل</div>
                <div
                  className={`${
                    darkMode ? "text-indigo-300" : "text-indigo-700"
                  }`}
                >
                  {(() => {
                    // استخدام الترتيب الصحيح للأيام
                    const workingDaysArray = orderedDays
                      .filter(([day]) => workingHours[day]?.isWorking)
                      .map(([_, dayName]) => dayName);

                    return workingDaysArray.length > 0
                      ? workingDaysArray.join("، ")
                      : "لم يتم تحديد أيام العمل";
                  })()}
                </div>
              </div>
            </div>

            {/* عرض ساعات العمل */}
            <div className="flex items-center p-3 rounded-lg bg-opacity-50 transition-all duration-300 hover:bg-opacity-70 hover:shadow-sm">
              <div
                className={`p-2 rounded-full ml-3 ${
                  darkMode ? "bg-indigo-900/50" : "bg-indigo-100"
                }`}
              >
                <Clock
                  size={20}
                  className={`${
                    darkMode ? "text-indigo-400" : "text-indigo-500"
                  } transition-colors duration-300`}
                />
              </div>
              <div
                className={`${darkMode ? "text-gray-300" : "text-gray-700"}`}
              >
                <div className="font-bold mb-1">ساعات العمل</div>
                <div
                  className={`${
                    darkMode ? "text-indigo-300" : "text-indigo-700"
                  }`}
                >
                  {(() => {
                    console.log(
                      "ساعات العمل في ProfileWorkingHours:",
                      workingHours
                    );

                    // البحث عن أول يوم عمل للحصول على ساعات العمل
                    const workingDay = orderedDays.find(
                      ([day]) => workingHours[day]?.isWorking
                    );

                    if (workingDay) {
                      const [day] = workingDay;
                      const hours = workingHours[day];
                      if (hours.start && hours.end) {
                        return `${hours.start} - ${hours.end}`;
                      }
                    }

                    return "لم يتم تحديد ساعات العمل";
                  })()}
                </div>
              </div>
            </div>
          </div>

          {/* ملاحظة توضيحية */}
          <div
            className={`mt-4 p-3 rounded-lg text-center text-sm ${
              darkMode
                ? "text-gray-400 bg-gray-800/50"
                : "text-gray-500 bg-gray-50/80"
            }`}
          >
            يتم تحديث حالة التوفر تلقائياً بناءً على أوقات الدوام المحددة
          </div>
        </div>
      )}
    </Card>
  );
};

export default ProfileWorkingHours;
