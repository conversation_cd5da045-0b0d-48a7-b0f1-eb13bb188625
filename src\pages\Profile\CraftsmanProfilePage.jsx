import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";

// استيراد المكونات
import Layout from "../../components/layout/Layout";
import LoadingState from "./components/LoadingState";
import ErrorState from "./components/ErrorState";
import CraftsmanInfo from "./components/CraftsmanInfo";
import CraftsmanContactInfo from "./components/CraftsmanContactInfo";
import CraftsmanBio from "./components/CraftsmanBio";
import CraftsmanGallery from "./components/CraftsmanGallery";
import ProfileLocation from "./components/ProfileLocation";
import CraftsmanSpecializations from "./components/CraftsmanSpecializations";
import CraftsmanReviews from "./components/CraftsmanReviews";
import BookingModal from "./components/BookingModal";
import GallerySection from "../../components/craftsman/GallerySection";
import { WorkGalleryManager } from "../../components/WorkGallery";
import ApiErrorNotification from "../../components/common/ApiErrorNotification";

// استيراد API ساعات العمل الجديد
import { getCraftsmanWorkingHours } from "../../api/workingHoursApi";

// استيراد دالة التحقق من التوفر
import { isAvailableNow } from "../../utils/availabilityUtils.js";

// استيراد المتاجر والخدمات
import useUserStore from "../../store/userStore";
import useCraftsmenStore from "../../store/craftsmenStore";
import useBookingStore from "../../store/bookingStore";
import useReviewStore from "../../store/reviewStore";
import useThemeStore from "../../store/themeStore";

const CraftsmanProfilePage = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const darkMode = useThemeStore((state) => state.darkMode);
  const user = useUserStore((state) => state.user);
  const craftsmen = useCraftsmenStore((state) => state.craftsmen);
  const fetchCraftsman = useCraftsmenStore((state) => state.fetchCraftsman);
  const addBooking = useBookingStore((state) => state.addBooking);
  const getCraftsmanReviews = useReviewStore(
    (state) => state.getCraftsmanReviews
  );

  // حالة المكون
  const [craftsman, setCraftsman] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [craftsmanReviews, setCraftsmanReviews] = useState([]);
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [reviewsError, setReviewsError] = useState(null); // حالة خطأ التقييمات
  const [bookingData, setBookingData] = useState({
    startDate: "",
    endDate: "",
    startTime: "",
    endTime: "",
    description: "",
  });
  const [bookingErrors, setBookingErrors] = useState({});
  const [bookingSuccess, setBookingSuccess] = useState(false);
  // حالة إرسال طلب الحجز
  const [isSubmitting, setIsSubmitting] = useState(false);

  // تحميل بيانات الحرفي
  useEffect(() => {
    const loadCraftsman = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log("Attempting to load craftsman with ID:", id);

        // البحث عن الحرفي في المتجر أولاً
        let craftsmanData = craftsmen.find((c) => {
          // تحويل المعرفات إلى نصوص للمقارنة
          const cId = c.id ? c.id.toString() : "";
          const c_Id = c._id ? c._id.toString() : "";
          const paramId = id ? id.toString() : "";

          console.log("Comparing IDs:", { cId, c_Id, paramId });
          return cId === paramId || c_Id === paramId;
        });

        console.log("Found craftsman in store:", craftsmanData ? "Yes" : "No");

        // إذا لم يتم العثور عليه، جلبه من الخادم
        if (!craftsmanData) {
          console.log("Fetching craftsman from server with ID:", id);
          craftsmanData = await fetchCraftsman(id);
          console.log("Server response:", craftsmanData);
        }

        if (!craftsmanData) {
          console.error("Craftsman not found with ID:", id);
          throw new Error("لم يتم العثور على الحرفي");
        }

        // طباعة بيانات الصورة للتصحيح
        console.log("Original image data:", {
          image: craftsmanData.image,
          user: craftsmanData.user,
          userImage: craftsmanData.user ? craftsmanData.user.image : null,
          userProfilePicture: craftsmanData.user
            ? craftsmanData.user.profilePicture
            : null,
          profilePicture: craftsmanData.profilePicture,
        });

        // تعيين صورة الحرفي
        // استخدام الصورة من البيانات إذا كانت موجودة، وإلا استخدام الصورة الافتراضية
        if (
          !craftsmanData.image ||
          craftsmanData.image === "" ||
          (craftsmanData.user &&
            (!craftsmanData.user.profilePicture ||
              craftsmanData.user.profilePicture === ""))
        ) {
          craftsmanData.image = "/img/user-avatar.svg?url";
        } else if (craftsmanData.image.startsWith("/uploads/")) {
          // إذا كان المسار يبدأ بـ /uploads/، أضف عنوان الخادم الافتراضي
          // نستخدم المسار النسبي أولاً، وإذا فشل سيتم التعامل معه في مكون LazyImage
          craftsmanData.image = craftsmanData.image;
        } else if (craftsmanData.image.includes("cinemaity.cinemaity.com")) {
          // إذا كان المسار من الموقع الحالي، نستخدم المسار النسبي
          try {
            const url = new URL(craftsmanData.image);
            craftsmanData.image = url.pathname;
          } catch (e) {
            console.error("Invalid URL:", craftsmanData.image);
            craftsmanData.image = "/img/user-avatar.svg?url";
          }
        }

        // طباعة مسار الصورة النهائي للتصحيح
        console.log("Final image path:", craftsmanData.image);

        // إذا كان الهاتف غير موجود، استخدم هاتف المستخدم إذا كان متاحاً
        if (
          !craftsmanData.phone &&
          craftsmanData.user &&
          craftsmanData.user.phone
        ) {
          craftsmanData.phone = craftsmanData.user.phone;
        }

        // تم تحميل بيانات الحرفي بنجاح

        // استخدام API الجديد لجلب ساعات العمل - فقط إذا لم تكن موجودة بالفعل
        if (
          !craftsmanData.workingHoursArray ||
          !Array.isArray(craftsmanData.workingHoursArray) ||
          craftsmanData.workingHoursArray.length === 0
        ) {
          try {
            const workingHoursResponse = await getCraftsmanWorkingHours(
              craftsmanData._id
            );
            if (workingHoursResponse && workingHoursResponse.workingHours) {
              craftsmanData.workingHoursArray =
                workingHoursResponse.workingHours;
            }
          } catch (workingHoursError) {
            // تجاهل الخطأ - سنستخدم البيانات الموجودة أو سننشئ مصفوفة فارغة لاحقاً
          }
        }

        // تأكد من أن workingHoursArray موجودة وتحتوي على بيانات
        if (
          !craftsmanData.workingHoursArray ||
          !Array.isArray(craftsmanData.workingHoursArray)
        ) {
          // إنشاء مصفوفة فارغة إذا لم تكن موجودة
          craftsmanData.workingHoursArray = [];
        }

        // تحقق من وجود بيانات في workingHours (كائن) وتحويلها إلى workingHoursArray إذا كانت فارغة
        if (
          craftsmanData.workingHoursArray.length === 0 &&
          craftsmanData.workingHours &&
          typeof craftsmanData.workingHours === "object"
        ) {
          console.log("تحويل بيانات من workingHours إلى workingHoursArray");

          // تحويل البيانات من الكائن إلى المصفوفة
          const daysOfWeek = [
            "saturday",
            "sunday",
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
          ];

          daysOfWeek.forEach((day) => {
            if (craftsmanData.workingHours[day]) {
              craftsmanData.workingHoursArray.push({
                day: day,
                isWorking: !!craftsmanData.workingHours[day].isWorking,
                start: craftsmanData.workingHours[day].start || "",
                end: craftsmanData.workingHours[day].end || "",
              });
            } else {
              // إضافة يوم بقيم افتراضية إذا لم يكن موجوداً
              craftsmanData.workingHoursArray.push({
                day: day,
                isWorking: false,
                start: "",
                end: "",
              });
            }
          });

          console.log(
            "تم تحويل البيانات من workingHours إلى workingHoursArray:",
            craftsmanData.workingHoursArray
          );
        }

        // تأكد من أن كل عنصر في workingHoursArray يحتوي على الخصائص المطلوبة

        // تحويل القيم المنطقية إلى قيم صريحة
        craftsmanData.workingHoursArray = craftsmanData.workingHoursArray.map(
          (day) => ({
            ...day,
            isWorking:
              day.isWorking === true ||
              day.isWorking === "true" ||
              day.isWorking === 1 ||
              day.isWorking === "1",
          })
        );

        // تحديث حالة التوفر بناءً على ساعات العمل
        craftsmanData.available = isAvailableNow(
          craftsmanData.workingHoursArray
        );

        // التحقق من الخصائص

        // تأكد من أن الخصائص مصفوفة
        if (!craftsmanData.features) {
          console.log("الخصائص غير موجودة، إنشاء مصفوفة فارغة");
          craftsmanData.features = [];
        } else if (!Array.isArray(craftsmanData.features)) {
          console.log("الخصائص موجودة ولكنها ليست مصفوفة، تحويلها إلى مصفوفة");
          craftsmanData.features = [craftsmanData.features];
        }

        // تمت معالجة الخصائص

        // تأكد من وجود اسم الحرفي
        if (
          !craftsmanData.name &&
          craftsmanData.user &&
          craftsmanData.user.name
        ) {
          craftsmanData.name = craftsmanData.user.name;
          // تم تعيين اسم الحرفي من بيانات المستخدم
        }

        setCraftsman(craftsmanData);
        setLoading(false);
      } catch (err) {
        console.error("Error loading craftsman:", err);
        setError(err.message || "حدث خطأ أثناء تحميل بيانات الحرفي");
        setLoading(false);
      }
    };

    loadCraftsman();
  }, [id, craftsmen, fetchCraftsman]);

  // تحميل تقييمات الحرفي
  useEffect(() => {
    const loadReviews = async () => {
      if (craftsman && (craftsman.id || craftsman._id)) {
        const craftsmanId = craftsman.id || craftsman._id;

        console.log("تحميل تقييمات الحرفي:", craftsmanId);

        // استخدام قيم التقييم المخزنة في كائن الحرفي إذا كانت موجودة
        if (
          craftsman.rating !== undefined &&
          craftsman.reviewCount !== undefined
        ) {
          console.log("استخدام قيم التقييم الموجودة في كائن الحرفي:", {
            rating: craftsman.rating,
            reviewCount: craftsman.reviewCount,
          });

          // تحميل التقييمات من الحجوزات
          if (craftsman.bookings && craftsman.bookings.length > 0) {
            console.log(
              "تحميل التقييمات من الحجوزات:",
              craftsman.bookings.length
            );

            const reviewPromises = [];
            const loadedReviews = [];

            // تحميل التقييمات من الحجوزات التي تحتوي على معرف تقييم
            for (const booking of craftsman.bookings) {
              if (booking.reviewId) {
                console.log(
                  "تحميل تقييم للحجز:",
                  booking.id,
                  "معرف التقييم:",
                  booking.reviewId
                );

                try {
                  // استخدام getReviewById لتحميل التقييم
                  const getReviewById = useReviewStore.getState().getReviewById;
                  const reviewPromise = getReviewById(booking.reviewId);
                  reviewPromises.push(reviewPromise);
                } catch (error) {
                  console.error("خطأ في تحميل التقييم:", error);
                }
              }
            }

            // انتظار تحميل جميع التقييمات
            if (reviewPromises.length > 0) {
              try {
                const reviews = await Promise.all(reviewPromises);
                for (const review of reviews) {
                  if (review) {
                    loadedReviews.push(review);
                  }
                }

                console.log("تم تحميل التقييمات:", loadedReviews.length);
                setCraftsmanReviews(loadedReviews);
              } catch (error) {
                console.error("خطأ في تحميل التقييمات:", error);
                setReviewsError(
                  "حدث خطأ أثناء تحميل التقييمات. سيتم عرض تقييم افتراضي."
                );
              }
            }
          } else {
            // محاولة تحميل التقييمات من الخادم مباشرة
            try {
              // استخدام getCraftsmanReviewsById لتحميل التقييمات
              const getCraftsmanReviewsById = useReviewStore.getState()
                .getCraftsmanReviewsById;
              if (getCraftsmanReviewsById) {
                const reviews = await getCraftsmanReviewsById(craftsmanId);
                console.log(
                  "تم تحميل التقييمات من الخادم مباشرة:",
                  reviews ? reviews.length : 0
                );
                setCraftsmanReviews(reviews || []);
              }
            } catch (error) {
              console.error("خطأ في تحميل التقييمات من الخادم مباشرة:", error);
              setReviewsError(
                "حدث خطأ أثناء تحميل التقييمات من الخادم. سيتم عرض تقييم افتراضي."
              );

              // استخدام التقييمات المخزنة محلياً
              const reviews = getCraftsmanReviews(craftsmanId);
              setCraftsmanReviews(reviews || []);
            }
          }
        } else {
          // إذا لم تكن قيم التقييم موجودة في كائن الحرفي، استخدم القيم الافتراضية
          console.log("استخدام القيم الافتراضية للتقييم");

          setCraftsman((prev) => ({
            ...prev,
            rating: 0,
            reviewCount: 0,
          }));

          setCraftsmanReviews([]);
        }
      }
    };

    loadReviews();
  }, [craftsman, getCraftsmanReviews]);

  // حساب متوسط التقييم - تم نقل هذه الوظيفة إلى مكون CraftsmanInfo
  // لذلك لم نعد بحاجة إلى هذا التأثير هنا

  // معالجة تغيير حقول الحجز
  const handleBookingInputChange = (e) => {
    const { name, value } = e.target;
    setBookingData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // إزالة رسالة الخطأ عند تغيير القيمة
    if (bookingErrors[name]) {
      setBookingErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  // التحقق من صحة بيانات الحجز
  const validateBookingData = () => {
    const errors = {};
    const today = new Date().toISOString().split("T")[0];

    if (!bookingData.startDate) {
      errors.startDate = "يرجى تحديد تاريخ البداية";
    } else if (bookingData.startDate < today) {
      errors.startDate = "لا يمكن اختيار تاريخ في الماضي";
    }

    if (!bookingData.endDate) {
      errors.endDate = "يرجى تحديد تاريخ النهاية";
    } else if (bookingData.endDate < bookingData.startDate) {
      errors.endDate = "يجب أن يكون تاريخ النهاية بعد تاريخ البداية";
    }

    if (!bookingData.startTime) {
      errors.startTime = "يرجى تحديد وقت البداية";
    }

    if (!bookingData.endTime) {
      errors.endTime = "يرجى تحديد وقت النهاية";
    } else if (
      bookingData.startDate === bookingData.endDate &&
      bookingData.endTime <= bookingData.startTime
    ) {
      errors.endTime = "يجب أن يكون وقت النهاية بعد وقت البداية";
    }

    if (!bookingData.description.trim()) {
      errors.description = "يرجى إدخال وصف للمشكلة";
    } else if (bookingData.description.trim().length < 2) {
      errors.description = "يجب أن يكون الوصف حرفين على الأقل";
    }

    return errors;
  };

  // معالجة إرسال طلب الحجز
  const handleBookingSubmit = async (e) => {
    e.preventDefault();

    // التحقق من تسجيل الدخول
    const userStore = useUserStore.getState();
    if (!userStore.isAuthenticated) {
      // تخزين بيانات الحجز في localStorage لاستعادتها بعد تسجيل الدخول
      localStorage.setItem(
        "pendingBooking",
        JSON.stringify({
          craftsmanId: id,
          returnUrl: `/craftsman/${id}`,
        })
      );
      navigate("/login", { state: { from: `/craftsman/${id}` } });
      return;
    }

    // التحقق من صحة البيانات
    const validationErrors = validateBookingData();
    if (Object.keys(validationErrors).length > 0) {
      setBookingErrors(validationErrors);
      return;
    }

    try {
      setIsSubmitting(true);

      // إنشاء طلب الحجز
      // استخدام المعرف الأصلي من API أو Firebase
      const craftsmanId = craftsman._id || craftsman.id;

      // استخدام معرف المستخدم من متجر المستخدم بدلاً من كائن المستخدم مباشرة
      const userStore = useUserStore.getState();
      const userId =
        userStore.userId || (user && (user._id || user.id || user.uid));

      console.log("معرفات الحجز:", {
        craftsmanId,
        userId,
        craftsmanOriginal: { id: craftsman.id, _id: craftsman._id },
        userStore: { userId: userStore.userId, userType: userStore.userType },
        userOriginal: user
          ? { id: user.id, _id: user._id, uid: user.uid }
          : "غير متوفر",
      });

      // طباعة معلومات الحرفي للتصحيح
      console.log("معلومات الحرفي:", {
        id: craftsmanId,
        name: craftsman.name,
        user: craftsman.user,
      });

      console.log("إضافة حجز جديد:", {
        craftsmanId,
        userId,
        craftsmanName: craftsman.name,
        userType: "client",
      });

      // الحصول على اسم الحرفي من كائن المستخدم إذا كان متاحًا
      const craftsmanName =
        craftsman.name || (craftsman.user && craftsman.user.name) || "حرفي";

      // طباعة اسم الحرفي للتصحيح
      console.log("اسم الحرفي المستخدم في الطلب:", craftsmanName);

      await addBooking({
        craftsmanId, // استخدام المعرف الأصلي
        craftsmanName, // استخدام اسم الحرفي
        craftsmanPhone:
          craftsman.phone || (craftsman.user && craftsman.user.phone) || "",
        clientId: userId, // استخدام معرف المستخدم كمعرف العميل
        clientName: user ? user.name || "" : userStore.user?.name || "",
        clientPhone: user ? user.phone || "" : userStore.user?.phone || "",
        date: bookingData.startDate, // استخدام تاريخ البداية كتاريخ الحجز
        time: bookingData.startTime, // استخدام وقت البداية كوقت الحجز
        startDate: bookingData.startDate,
        endDate: bookingData.endDate,
        startTime: bookingData.startTime,
        endTime: bookingData.endTime,
        description: bookingData.description,
        status: "pending",
      });

      // إعادة تعيين البيانات وإغلاق النافذة
      setBookingSuccess(true);
      setBookingData({
        startDate: "",
        endDate: "",
        startTime: "",
        endTime: "",
        description: "",
      });
      setShowBookingModal(false);

      // إظهار رسالة النجاح لمدة 3.5 ثوانٍ (لتتزامن مع إنشاء الإشعار)
      setTimeout(() => {
        setBookingSuccess(false);
      }, 3500);
    } catch (err) {
      console.error("Error creating booking:", err);
      setBookingErrors({
        general: err.message || "حدث خطأ أثناء إنشاء الحجز",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // ملاحظة: تم إزالة دالة تسجيل الخروج لأنها غير مستخدمة في هذا المكون

  // عرض حالة التحميل
  if (loading) {
    return (
      <div
        className={`min-h-screen flex items-center justify-center ${
          darkMode
            ? "bg-gray-900 text-white"
            : "bg-gradient-to-br from-blue-50 to-indigo-100 text-black"
        } transition-colors duration-300`}
      >
        <LoadingState darkMode={darkMode} />
      </div>
    );
  }

  // عرض حالة الخطأ
  if (error || !craftsman) {
    return (
      <div
        className={`min-h-screen flex items-center justify-center ${
          darkMode
            ? "bg-gray-900 text-white"
            : "bg-gradient-to-br from-blue-50 to-indigo-100 text-black"
        } transition-colors duration-300`}
      >
        <ErrorState error={error} darkMode={darkMode} />
      </div>
    );
  }

  return (
    <Layout>
      {/* إشعار خطأ التقييمات */}
      {reviewsError && (
        <ApiErrorNotification
          message={reviewsError}
          type="warning"
          show={true}
          onClose={() => setReviewsError(null)}
          autoCloseTime={7000}
        />
      )}
      <div className="container mx-auto px-4 py-8">
        {/* رسالة نجاح الحجز - توست ثابت في المنتصف */}
        {bookingSuccess && (
          <div
            className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none"
            style={{ position: "fixed" }}
          >
            <div
              className={`p-6 rounded-lg shadow-2xl ${
                darkMode ? "bg-green-900/95" : "bg-green-100"
              } ${
                darkMode ? "text-green-200" : "text-green-700"
              } text-center max-w-md w-full border ${
                darkMode ? "border-green-700" : "border-green-300"
              } pointer-events-auto animate-toast`}
            >
              <div className="flex flex-col items-center">
                <div
                  className={`w-16 h-16 rounded-full flex items-center justify-center mb-3 ${
                    darkMode ? "bg-green-800" : "bg-green-200"
                  }`}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className={`h-8 w-8 ${
                      darkMode ? "text-green-300" : "text-green-600"
                    }`}
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M5 13l4 4L19 7"
                    />
                  </svg>
                </div>
                <h3 className="font-bold text-xl mb-2">
                  تم إرسال طلب الحجز بنجاح!
                </h3>
                <p className="text-lg">
                  سيتم إشعارك عندما يقوم الحرفي بالرد على طلبك.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* الشريط الجانبي */}
          <div className="md:col-span-1">
            {/* معلومات الحرفي */}
            <CraftsmanInfo
              craftsman={craftsman}
              darkMode={darkMode}
              onBookingClick={() => setShowBookingModal(true)}
              reviews={craftsmanReviews}
            />

            {/* معلومات الاتصال */}
            <CraftsmanContactInfo craftsman={craftsman} darkMode={darkMode} />
          </div>

          {/* المحتوى الرئيسي */}
          <div className="md:col-span-2">
            {/* معرض الأعمال المطور */}
            <WorkGalleryManager
              craftsmanId={craftsman.id || craftsman._id}
              isEditable={false}
              title="معرض الأعمال"
            />

            {/* معرض الأعمال القديم (للمقارنة - يمكن حذفه لاحقاً) */}
            {craftsman.gallery && craftsman.gallery.length > 0 && (
              <div className="mt-6">
                <GallerySection craftsmanId={craftsman.id || craftsman._id} />
              </div>
            )}

            {/* التقييمات */}
            <CraftsmanReviews reviews={craftsmanReviews} darkMode={darkMode} />
          </div>
        </div>

        {/* نافذة الحجز */}
        {showBookingModal && (
          <BookingModal
            darkMode={darkMode}
            bookingData={bookingData}
            bookingErrors={bookingErrors}
            onClose={() => setShowBookingModal(false)}
            onInputChange={handleBookingInputChange}
            onSubmit={handleBookingSubmit}
            isSubmitting={isSubmitting}
          />
        )}
      </div>
    </Layout>
  );
};

export default CraftsmanProfilePage;
