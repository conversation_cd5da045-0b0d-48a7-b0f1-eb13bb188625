import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import Layout from "../../components/layout/Layout";
import Input from "../../components/common/Input";
import Button from "../../components/common/Button";
import LoadingButton from "../../components/common/LoadingButton";
import CountryCodeSelectButtons from "../../components/common/CountryCodeSelectButtons";
import useUserStore from "../../store/userStore";
import useThemeStore from "../../store/themeStore";
import useSiteSettingsStore from "../../store/siteSettingsStore";
import authService from "../../services/authService";
import toastUtils from "../../utils/toastUtils";
import supabaseAuthService from "../../services/supabaseAuthService";

const ClientRegisterPage = () => {
  const [registerType, setRegisterType] = useState("email");
  const [phoneVerification, setPhoneVerification] = useState({
    sent: false,
    sending: false,
    verified: false,
    otp: "",
    verifying: false,
  });

  // تحميل الحالة الأولية من localStorage إذا كانت موجودة
  const loadInitialState = () => {
    const savedState = localStorage.getItem("otpAttemptsState");
    if (savedState) {
      const parsedState = JSON.parse(savedState);
      // التحقق من أن البيانات ليست قديمة جداً
      if (Date.now() - parsedState.timestamp < 3600 * 1000) {
        return {
          attempts: parsedState.attempts,
          cooldown: parsedState.cooldown,
          hourlyBlock: parsedState.hourlyBlock,
          resendCooldown: parsedState.resendCooldown,
        };
      } else {
        localStorage.removeItem("otpAttemptsState");
      }
    }
    return {
      attempts: 0,
      cooldown: 0,
      hourlyBlock: 0,
      resendCooldown: 0,
    };
  };

  const initialState = loadInitialState();
  const [otpAttempts, setOtpAttempts] = useState(initialState.attempts);
  const [otpCooldown, setOtpCooldown] = useState(initialState.cooldown);
  const [hourlyBlock, setHourlyBlock] = useState(initialState.hourlyBlock);
  const [resendCooldown, setResendCooldown] = useState(
    initialState.resendCooldown
  );

  const navigate = useNavigate();
  const location = useLocation();
  const login = useUserStore((state) => state.login);
  const darkMode = useThemeStore((state) => state.darkMode);
  const { settings, fetchSettings } = useSiteSettingsStore();
  const googleProfile = location.state?.googleProfile;

  const [step, setStep] = useState(1);
  const [emailVerificationSent, setEmailVerificationSent] = useState(false);
  const [emailVerification, setEmailVerification] = useState({
    sent: false,
    sending: false,
    verified: false,
    checking: false,
  });
  const [formData, setFormData] = useState({
    name: googleProfile?.name || "",
    phone: "",
    countryCode: "+963",
    email: googleProfile?.email || "",
    password: "",
    confirmPassword: "",
    otp: "",
    googleId: googleProfile?.googleId || "",
    image: googleProfile?.picture || "",
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [resendCountdown, setResendCountdown] = useState(0);
  const recaptchaContainerRef = useRef(null);

  // جلب إعدادات الموقع
  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // حفظ الحالة في localStorage عند التغيير
  useEffect(() => {
    const stateToSave = {
      attempts: otpAttempts,
      cooldown: otpCooldown,
      hourlyBlock: hourlyBlock,
      resendCooldown: resendCooldown,
      timestamp: Date.now(),
    };
    localStorage.setItem("otpAttemptsState", JSON.stringify(stateToSave));
  }, [otpAttempts, otpCooldown, hourlyBlock, resendCooldown]);

  // تنظيف reCAPTCHA عند تفكيك المكون
  useEffect(() => {
    return () => {
      if (window.recaptchaVerifier) {
        try {
          window.recaptchaVerifier.clear();
          window.recaptchaVerifier = null;
        } catch (error) {
          console.error("Error clearing recaptcha:", error);
        }
      }
    };
  }, []);

  // استعادة بيانات التسجيل من localStorage عند تحميل الصفحة
  useEffect(() => {
    const savedRegistrationData = localStorage.getItem("pendingRegistration");
    if (savedRegistrationData) {
      try {
        const registrationData = JSON.parse(savedRegistrationData);
        console.log("Found saved registration data:", registrationData);

        const oneHour = 60 * 60 * 1000;
        if (Date.now() - registrationData.timestamp < oneHour) {
          setFormData((prev) => ({
            ...prev,
            name: registrationData.name || prev.name,
            phone: registrationData.phone || prev.phone,
            countryCode: registrationData.countryCode || prev.countryCode,
            email: registrationData.email || prev.email,
            googleId: registrationData.googleId || prev.googleId,
            image: registrationData.image || prev.image,
          }));

          setEmailVerification((prev) => ({ ...prev, sent: true }));
          setStep(2);
        } else {
          localStorage.removeItem("pendingRegistration");
          console.log("Removed old registration data");
        }
      } catch (error) {
        console.error("Error parsing saved registration data:", error);
        localStorage.removeItem("pendingRegistration");
      }
    }
  }, []);

  // إذا كان هناك ملف شخصي من Google، قم بملء البيانات تلقائيًا
  useEffect(() => {
    if (googleProfile) {
      setFormData((prev) => ({
        ...prev,
        name: googleProfile.name || prev.name,
        email: googleProfile.email || prev.email,
        googleId: googleProfile.googleId || prev.googleId,
        image: googleProfile.picture || prev.image,
      }));
    }
  }, [googleProfile]);

  // التحقق من تأكيد البريد الإلكتروني عند تحميل الصفحة
  useEffect(() => {
    const checkEmailConfirmation = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const confirmed = urlParams.get("confirmed");

      if (
        confirmed === "true" ||
        window.location.hash.includes("access_token")
      ) {
        console.log("Email confirmation detected in URL");
        setEmailVerification((prev) => ({ ...prev, checking: true }));

        try {
          const confirmationResult = await supabaseAuthService.handleEmailConfirmation();

          if (confirmationResult.success && confirmationResult.confirmed) {
            console.log(
              "Email confirmed successfully:",
              confirmationResult.user
            );

            const savedRegistrationData = localStorage.getItem(
              "pendingRegistration"
            );
            if (savedRegistrationData) {
              const registrationData = JSON.parse(savedRegistrationData);
              console.log(
                "Restored registration data from localStorage:",
                registrationData
              );

              setFormData((prev) => ({
                ...prev,
                uid: confirmationResult.user.uid,
                email: confirmationResult.user.email,
                name:
                  registrationData.name ||
                  confirmationResult.user.name ||
                  prev.name,
                phone: registrationData.phone || prev.phone,
                countryCode: registrationData.countryCode || prev.countryCode,
                googleId: registrationData.googleId || prev.googleId,
                image: registrationData.image || prev.image,
              }));
            } else {
              setFormData((prev) => ({
                ...prev,
                uid: confirmationResult.user.uid,
                email: confirmationResult.user.email,
                name: confirmationResult.user.name || prev.name,
              }));
            }

            setEmailVerification((prev) => ({
              ...prev,
              verified: true,
              checking: false,
              sent: true,
            }));

            if (step === 1) {
              setStep(2);
            }

            window.history.replaceState(
              {},
              document.title,
              window.location.pathname
            );
          } else {
            setEmailVerification((prev) => ({ ...prev, checking: false }));
          }
        } catch (error) {
          console.error("Error checking email confirmation:", error);
          setEmailVerification((prev) => ({ ...prev, checking: false }));
        }
      }
    };

    checkEmailConfirmation();
  }, [step]);

  // مراقبة تغييرات حالة المصادقة
  useEffect(() => {
    const { unsubscribe } = supabaseAuthService.onAuthStateChange(
      (authState) => {
        console.log("Auth state changed:", authState);

        if (authState.event === "SIGNED_IN" && authState.user) {
          console.log("User signed in:", authState.user);

          if (authState.user.emailVerified) {
            setEmailVerification((prev) => ({
              ...prev,
              verified: true,
              checking: false,
            }));

            const savedRegistrationData = localStorage.getItem(
              "pendingRegistration"
            );
            if (savedRegistrationData) {
              try {
                const registrationData = JSON.parse(savedRegistrationData);
                console.log(
                  "Restoring registration data from localStorage:",
                  registrationData
                );

                setFormData((prev) => ({
                  ...prev,
                  uid: authState.user.uid,
                  email: authState.user.email,
                  name:
                    registrationData.name || authState.user.name || prev.name,
                  phone: registrationData.phone || prev.phone,
                  countryCode: registrationData.countryCode || prev.countryCode,
                  googleId: registrationData.googleId || prev.googleId,
                  image: registrationData.image || prev.image,
                }));
              } catch (error) {
                console.error("Error parsing saved registration data:", error);
                setFormData((prev) => ({
                  ...prev,
                  uid: authState.user.uid,
                  email: authState.user.email,
                  name: authState.user.name || prev.name,
                }));
              }
            } else {
              setFormData((prev) => ({
                ...prev,
                uid: authState.user.uid,
                email: authState.user.email,
                name: authState.user.name || prev.name,
              }));
            }
          }
        }
      }
    );

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, []);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  const validateStep1 = () => {
    let isValid = true;
    const newErrors = {};

    if (!formData.name) {
      newErrors.name = "الاسم مطلوب";
      isValid = false;
    } else if (formData.name.length < 3) {
      newErrors.name = "يجب أن يتكون الاسم من 3 أحرف على الأقل";
      isValid = false;
    }

    if (registerType === "phone") {
      if (!formData.phone) {
        newErrors.phone = "رقم الهاتف مطلوب";
        isValid = false;
      } else {
        if (formData.countryCode === "+963") {
          const cleanedPhone = formData.phone
            .replace(/^0+/, "")
            .replace(/\D/g, "");
          if (!/^9\d{8}$/.test(cleanedPhone)) {
            newErrors.phone =
              "يرجى إدخال رقم هاتف سوري صالح (9 أرقام تبدأ بـ 9)";
            isValid = false;
          }
        } else if (formData.countryCode === "+1") {
          const cleanedPhone = formData.phone.replace(/\D/g, "");
          if (cleanedPhone.length !== 10) {
            newErrors.phone = "يرجى إدخال رقم هاتف أمريكي صالح (10 أرقام)";
            isValid = false;
          }
        } else {
          const cleanedPhone = formData.phone
            .replace(/^0+/, "")
            .replace(/\D/g, "");
          if (cleanedPhone.length < 7 || cleanedPhone.length > 15) {
            newErrors.phone = "يرجى إدخال رقم هاتف صالح";
            isValid = false;
          }
        }
      }
    }

    if (registerType === "email") {
      if (!formData.email) {
        newErrors.email = "البريد الإلكتروني مطلوب";
        isValid = false;
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        newErrors.email = "يرجى إدخال بريد إلكتروني صالح";
        isValid = false;
      }
    }

    if (!formData.password) {
      newErrors.password = "كلمة المرور مطلوبة";
      isValid = false;
    } else if (formData.password.length < 6) {
      newErrors.password = "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل";
      isValid = false;
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "تأكيد كلمة المرور مطلوب";
      isValid = false;
    } else if (formData.confirmPassword !== formData.password) {
      newErrors.confirmPassword = "كلمات المرور غير متطابقة";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // إضافة تأثير العد التنازلي لإعادة إرسال رمز التحقق
  useEffect(() => {
    let timer;
    if (resendCountdown > 0) {
      timer = setTimeout(() => {
        setResendCountdown(resendCountdown - 1);
      }, 1000);
    } else {
      setResendDisabled(false);
    }
    return () => clearTimeout(timer);
  }, [resendCountdown]);

  // عداد إعادة إرسال OTP (60 ثانية)
  useEffect(() => {
    let timer;
    if (resendCooldown > 0) {
      timer = setTimeout(() => {
        setResendCooldown(resendCooldown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [resendCooldown]);

  // عداد الحظر لمدة ساعة
  useEffect(() => {
    let timer;
    if (hourlyBlock > 0) {
      timer = setTimeout(() => {
        setHourlyBlock(hourlyBlock - 1);
      }, 1000);
    } else if (hourlyBlock === 0 && otpAttempts >= 3) {
      setOtpAttempts(0);
      localStorage.removeItem("otpAttemptsState");
    }
    return () => clearTimeout(timer);
  }, [hourlyBlock, otpAttempts]);

  const sendVerificationCode = async () => {
    try {
      setEmailVerification((prev) => ({ ...prev, sending: true }));
      setErrors({});

      const registrationData = {
        name: formData.name,
        phone: formData.phone,
        countryCode: formData.countryCode,
        email: formData.email,
        googleId: formData.googleId,
        image: formData.image,
        timestamp: Date.now(),
      };

      localStorage.setItem(
        "pendingRegistration",
        JSON.stringify(registrationData)
      );
      console.log("Saved registration data to localStorage:", registrationData);

      const response = await supabaseAuthService.registerWithEmailAndPassword(
        formData.email,
        formData.password,
        formData.name
      );

      if (!response.success) {
        throw new Error(response.error.message);
      }

      console.log("Email registration response:", response);

      setFormData((prev) => ({
        ...prev,
        uid: response.user.uid,
      }));

      setEmailVerification((prev) => ({
        ...prev,
        sent: true,
        sending: false,
        verified: response.user.emailVerified || false,
      }));

      setResendDisabled(true);
      setResendCountdown(60);

      setStep(2);

      return true;
    } catch (error) {
      console.error("Error sending verification code:", error);

      setEmailVerification((prev) => ({ ...prev, sending: false }));

      toastUtils.showToast(
        error.message || "حدث خطأ أثناء إرسال رمز التحقق",
        "error",
        3000
      );

      return false;
    }
  };

  const handleResendOtp = async () => {
    if (!resendDisabled) {
      setIsLoading(true);
      try {
        // إعادة إرسال رابط التحقق باستخدام Supabase
        const result = await supabaseAuthService.resendEmailVerification(
          formData.email
        );

        if (result.success) {
          setResendDisabled(true);
          setResendCountdown(60);

          toastUtils.showToast(
            "تم إعادة إرسال رابط التحقق بنجاح. يرجى التحقق من بريدك الإلكتروني.",
            "success",
            3000
          );
        } else {
          throw new Error(
            result.error?.message || "فشل في إعادة إرسال رابط التحقق"
          );
        }
      } catch (error) {
        console.error("Error resending verification email:", error);
        toastUtils.showToast(
          error.message || "حدث خطأ أثناء إعادة إرسال رابط التحقق",
          "error",
          3000
        );
      } finally {
        setIsLoading(false);
      }
    }

    const handleNextStep = async (e) => {
      e.preventDefault();

      if (validateStep1()) {
        setIsLoading(true);

        try {
          if (registerType === "phone") {
            if (!phoneVerification.verified) {
              toastUtils.showToast(
                "يرجى تأكيد رقم الهاتف قبل المتابعة",
                "error",
                2000
              );
              setIsLoading(false);
              return;
            }

            const cleanedPhone = formData.phone
              .replace(/\D/g, "")
              .replace(/^0+/, "");
            const countryCode = formData.countryCode || "+963";
            let fullPhoneNumber = countryCode + cleanedPhone;

            const phoneCheckResponse = await authService.checkPhoneExists(
              fullPhoneNumber
            );
            if (phoneCheckResponse.exists) {
              setErrors({
                ...errors,
                phone: "رقم الهاتف مستخدم بالفعل، الرجاء استخدام رقم هاتف آخر",
              });
              setIsLoading(false);
              return;
            }

            const userData = {
              name: formData.name,
              phone: fullPhoneNumber,
              email: `${fullPhoneNumber.replace("+", "")}@phone.com`,
              password: formData.password,
              userType: "client",
              googleId: formData.googleId || null,
              image: formData.image || null,
            };

            const response = await authService.register(userData);
            login(response.user || userData, "client");
            navigate("/home");
          } else if (registerType === "email") {
            const emailCheckResponse = await authService.checkEmailExists(
              formData.email
            );
            if (emailCheckResponse.exists) {
              setErrors({
                ...errors,
                email:
                  "البريد الإلكتروني مستخدم بالفعل، الرجاء استخدام بريد إلكتروني آخر",
              });
              setIsLoading(false);
              return;
            }

            await sendVerificationCode();
          }
        } catch (error) {
          console.error("Error in handleNextStep:", error);
          setErrors({
            ...errors,
            general: error.message || "حدث خطأ أثناء التحقق من البيانات",
          });
          setIsLoading(false);
        }
      }
    };

    const handleRegister = async (e) => {
      e.preventDefault();

      setIsLoading(true);
      try {
        if (!emailVerification.verified) {
          setErrors({
            ...errors,
            general: "يرجى تأكيد بريدك الإلكتروني قبل المتابعة",
          });
          setIsLoading(false);
          return;
        }

        const cleanedPhone = formData.phone
          .replace(/\D/g, "")
          .replace(/^0+/, "");
        const countryCode = formData.countryCode || "+963";
        const fullPhoneNumber = countryCode + cleanedPhone;

        const userData = {
          uid: formData.uid,
          name: formData.name,
          phone: fullPhoneNumber,
          email: formData.email,
          userType: "client",
          googleId: formData.googleId || null,
          image: formData.image || null,
          isSupabase: true, // تحديد أن هذا مستخدم Supabase
        };

        console.log("Registering client with Supabase:", userData);

        const response = await authService.registerFirebaseUser(userData);
        console.log("Registration response:", response);

        localStorage.removeItem("pendingRegistration");
        console.log("Cleared pending registration data from localStorage");

        login(response.user || userData, "client");

        navigate("/home");
      } catch (error) {
        console.error("Registration error:", error);
        setErrors({
          ...errors,
          general: error.message || "حدث خطأ أثناء التسجيل",
        });
      } finally {
        setIsLoading(false);
      }
    };
  };

  const handleNextStep = async (e) => {
    e.preventDefault();

    if (validateStep1()) {
      setIsLoading(true);

      try {
        if (registerType === "phone") {
          // التحقق من التحقق من رقم الهاتف
          if (!phoneVerification.verified) {
            toastUtils.showToast(
              "يرجى تأكيد رقم الهاتف قبل المتابعة",
              "error",
              2000
            );
            setIsLoading(false);
            return;
          }

          // تنسيق رقم الهاتف مع رمز الدولة
          const cleanedPhone = formData.phone
            .replace(/\D/g, "")
            .replace(/^0+/, "");
          const countryCode = formData.countryCode || "+963";
          let fullPhoneNumber = countryCode + cleanedPhone;

          // التحقق من عدم وجود حساب بنفس رقم الهاتف
          const phoneCheckResponse = await authService.checkPhoneExists(
            fullPhoneNumber
          );
          if (phoneCheckResponse.exists) {
            setErrors({
              ...errors,
              phone: "رقم الهاتف مستخدم بالفعل، الرجاء استخدام رقم هاتف آخر",
            });
            setIsLoading(false);
            return;
          }

          // تسجيل المستخدم مباشرة (تم التحقق من رقم الهاتف بالفعل)
          const userData = {
            name: formData.name,
            phone: fullPhoneNumber,
            email: `${fullPhoneNumber.replace("+", "")}@phone.com`, // بريد وهمي للتسجيل بالهاتف
            password: formData.password,
            userType: "client",
            googleId: formData.googleId || null,
            image: formData.image || null,
          };

          const response = await authService.register(userData);
          login(response.user || userData, "client");
          navigate("/home");
        } else if (registerType === "email") {
          // التحقق من عدم وجود حساب بنفس البريد الإلكتروني
          const emailCheckResponse = await authService.checkEmailExists(
            formData.email
          );
          if (emailCheckResponse.exists) {
            setErrors({
              ...errors,
              email:
                "البريد الإلكتروني مستخدم بالفعل، الرجاء استخدام بريد إلكتروني آخر",
            });
            setIsLoading(false);
            return;
          }

          // إرسال رمز التحقق عبر البريد الإلكتروني والانتقال للخطوة التالية
          await sendVerificationCode();
        }
      } catch (error) {
        console.error("Error in handleNextStep:", error);
        toastUtils.showToast(
          error.message || "حدث خطأ أثناء التحقق من البيانات",
          "error",
          3000
        );
        setIsLoading(false);
      }
    }
  };

  const handleRegister = async (e) => {
    e.preventDefault();

    // التحقق من تأكيد البريد الإلكتروني
    setIsLoading(true);
    try {
      // التحقق من حالة التحقق
      if (!emailVerification.verified) {
        toastUtils.showToast(
          "يرجى تأكيد بريدك الإلكتروني قبل المتابعة",
          "warning",
          3000
        );
        setIsLoading(false);
        return;
      }

      // إعداد بيانات المستخدم للتخزين في قاعدة البيانات الخاصة بنا
      // تنسيق رقم الهاتف مع رمز الدولة
      const cleanedPhone = formData.phone.replace(/\D/g, "").replace(/^0+/, "");
      const countryCode = formData.countryCode || "+963";
      const fullPhoneNumber = countryCode + cleanedPhone;

      const userData = {
        uid: formData.uid,
        name: formData.name,
        phone: fullPhoneNumber,
        email: formData.email,
        userType: "client",
        // إضافة معرف Google وصورة الملف الشخصي إذا كان التسجيل باستخدام Google
        googleId: formData.googleId || null,
        image: formData.image || null,
        isSupabase: true, // تحديد أن هذا مستخدم Supabase
      };

      console.log("Registering client with Supabase:", userData);

      // حفظ بيانات المستخدم في قاعدة البيانات الخاصة بنا
      const response = await authService.registerFirebaseUser(userData);
      console.log("Registration response:", response);

      // تنظيف localStorage بعد إكمال التسجيل بنجاح
      localStorage.removeItem("pendingRegistration");
      console.log("Cleared pending registration data from localStorage");

      // تسجيل دخول المستخدم في المخزن المحلي
      login(response.user || userData, "client");

      // الانتقال إلى الصفحة الرئيسية
      navigate("/home");
    } catch (error) {
      console.error("Registration error:", error);
      toastUtils.showToast(
        error.message || "حدث خطأ أثناء التسجيل",
        "error",
        3000
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout hideFooter>
      <div
        className={`min-h-screen py-12 ${
          darkMode
            ? "bg-gray-900"
            : "bg-gradient-to-br from-blue-50 to-indigo-100"
        } transition-colors duration-300`}
      >
        <div className="container mx-auto px-4">
          <motion.div
            className={`max-w-lg mx-auto rounded-lg shadow-xl overflow-hidden border-2 ${
              darkMode
                ? "bg-gray-800 border-gray-700"
                : "bg-white border-indigo-200"
            } transition-colors duration-300`}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <div className="p-6">
              <h2
                className={`text-2xl font-bold text-center mb-4 ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                } relative transition-colors duration-300`}
              >
                <span className="relative z-10">إنشاء حساب جديد</span>
                <span
                  className={`absolute bottom-0 left-0 right-0 h-3 ${
                    darkMode ? "bg-indigo-500" : "bg-indigo-300"
                  } opacity-40 transform -rotate-1 z-0`}
                ></span>
              </h2>

              <div className="mb-4 text-center">
                <p
                  className={`${
                    darkMode ? "text-gray-300" : "text-gray-600"
                  } text-sm`}
                >
                  سجل كطالب خدمة للوصول إلى أفضل الحرفيين في منطقتك
                </p>
              </div>

              {/* عنصر reCAPTCHA غير مرئي */}
              <div id="recaptcha-container" ref={recaptchaContainerRef}></div>

              {step === 1 ? (
                <form onSubmit={handleNextStep}>
                  <Input
                    label="الاسم الكامل"
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="أدخل اسمك الكامل"
                    error={errors.name}
                    required
                    className="text-base py-2 border-indigo-200 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 mb-3"
                  />

                  {/* أزرار اختيار نوع التسجيل (بريد أو هاتف) */}
                  <div className="mb-4">
                    <label className="block font-medium mb-2 text-indigo-700">
                      طريقة التفعيل
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      <button
                        type="button"
                        className={`p-3 rounded-md border transition-all duration-300 shadow-sm relative overflow-hidden group ${
                          registerType === "email"
                            ? "border-indigo-500 bg-gradient-to-r from-blue-50 to-indigo-100 text-indigo-700 shadow-md"
                            : "border-indigo-200 text-indigo-600 hover:bg-indigo-50"
                        }`}
                        onClick={() => {
                          setRegisterType("email");
                          setFormData((prev) => ({
                            ...prev,
                            email: "",
                            phone: "",
                          }));
                          setErrors({});
                        }}
                      >
                        <span className="relative z-10 flex items-center justify-center gap-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                            />
                          </svg>
                          البريد الإلكتروني
                        </span>
                      </button>
                      <button
                        type="button"
                        className={`p-3 rounded-md border transition-all duration-300 shadow-sm relative overflow-hidden group ${
                          registerType === "phone"
                            ? "border-indigo-500 bg-gradient-to-r from-blue-50 to-indigo-100 text-indigo-700 shadow-md"
                            : "border-indigo-200 text-indigo-600 hover:bg-indigo-50"
                        }`}
                        onClick={() => {
                          setRegisterType("phone");
                          setFormData((prev) => ({
                            ...prev,
                            phone: "",
                            email: "",
                          }));
                          setErrors({});
                        }}
                      >
                        <span className="relative z-10 flex items-center justify-center gap-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                            />
                          </svg>
                          رقم الهاتف
                        </span>
                      </button>
                    </div>
                  </div>

                  {/* حقل إدخال رقم الهاتف مع رمز الدولة وإرسال رمز التحقق */}
                  {registerType === "phone" && (
                    <div className="mb-3">
                      <label className="block text-indigo-700 font-medium mb-2">
                        رقم الهاتف
                      </label>
                      <div className="grid grid-cols-3 gap-2">
                        <div className="col-span-2 rounded-md border border-indigo-200 focus-within:border-indigo-500 focus-within:ring focus-within:ring-indigo-200 focus-within:ring-opacity-50">
                          <input
                            type="tel"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            placeholder="أدخل رقم الهاتف"
                            required
                            className="w-full py-2 px-3 outline-none border-0 focus:ring-0 text-base"
                            dir="ltr"
                          />
                        </div>
                        <div className="relative rounded-md border border-indigo-200 focus-within:border-indigo-500 focus-within:ring focus-within:ring-indigo-200 focus-within:ring-opacity-50">
                          <CountryCodeSelectButtons
                            value={formData.countryCode || "+963"}
                            onChange={(code) => {
                              setFormData({ ...formData, countryCode: code });
                            }}
                            disabled={isLoading}
                          />
                        </div>
                      </div>
                      {errors.phone && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.phone}
                        </p>
                      )}
                      {/* عرض عدد المحاولات المتبقية */}
                      {otpAttempts > 0 && (
                        <div className="mt-2 text-sm text-gray-600">
                          المحاولات المتبقية: {3 - otpAttempts} من 3
                        </div>
                      )}

                      {/* زر إرسال رمز التحقق أو رسالة الحظر */}
                      <div className="mt-2 flex gap-2 items-center">
                        {otpAttempts >= 3 && hourlyBlock > 0 ? (
                          // عرض رسالة الحظر بدلاً من الزر
                          <div className="w-full p-3 bg-red-100 border border-red-300 rounded-lg text-center">
                            <p className="text-red-700 font-medium">
                              تم تجاوز الحد الأقصى للمحاولات
                            </p>
                            <p className="text-red-600 text-sm mt-1">
                              يمكنك المحاولة مرة أخرى بعد:{" "}
                              {Math.floor(hourlyBlock / 3600)}:
                              {Math.floor((hourlyBlock % 3600) / 60)
                                .toString()
                                .padStart(2, "0")}
                              :{(hourlyBlock % 60).toString().padStart(2, "0")}
                            </p>
                          </div>
                        ) : (
                          // عرض الزر العادي
                          <Button
                            type="button"
                            variant="secondary"
                            disabled={
                              phoneVerification.sending ||
                              !formData.phone ||
                              resendCooldown > 0
                            }
                            onClick={async () => {
                              setPhoneVerification((prev) => ({
                                ...prev,
                                sending: true,
                              }));
                              try {
                                // معالجة الرقم السوري ليكون بالشكل الصحيح
                                let phoneToSend = formData.phone;
                                if (
                                  (formData.countryCode === "+963" ||
                                    formData.countryCode === "963") &&
                                  phoneToSend.startsWith("0")
                                ) {
                                  phoneToSend =
                                    formData.countryCode + phoneToSend.slice(1);
                                } else {
                                  phoneToSend =
                                    formData.countryCode + phoneToSend;
                                }
                                const response = await authService.sendOtpToPhone(
                                  phoneToSend
                                );
                                if (response.success) {
                                  setPhoneVerification({
                                    sent: true,
                                    sending: false,
                                    verified: false,
                                    otp: "",
                                    verifying: false,
                                  });
                                  // زيادة عدد المحاولات وبدء العداد
                                  const newAttempts = otpAttempts + 1;
                                  setOtpAttempts(newAttempts);
                                  setResendCooldown(60); // 60 ثانية

                                  // إذا كانت هذه المحاولة الثالثة، ابدأ الحظر لمدة ساعة
                                  if (newAttempts >= 3) {
                                    setHourlyBlock(3600); // ساعة واحدة = 3600 ثانية
                                    toastUtils.showToast(
                                      "تم إرسال رمز التحقق. هذه المحاولة الأخيرة لمدة ساعة.",
                                      "warning",
                                      3000
                                    );
                                  } else {
                                    toastUtils.showToast(
                                      "تم إرسال رمز التحقق بنجاح",
                                      "success",
                                      2000
                                    );
                                  }
                                } else {
                                  throw new Error(
                                    response.message ||
                                      "فشل في إرسال رمز التحقق"
                                  );
                                }
                              } catch (error) {
                                setPhoneVerification((prev) => ({
                                  ...prev,
                                  sending: false,
                                }));
                                setErrors({
                                  ...errors,
                                  phone:
                                    error.message ||
                                    "حدث خطأ أثناء إرسال رمز التحقق",
                                });
                              }
                            }}
                          >
                            {phoneVerification.sending
                              ? "جاري الإرسال..."
                              : resendCooldown > 0
                              ? `إعادة الإرسال (${resendCooldown})`
                              : "إرسال رمز التحقق"}
                          </Button>
                        )}
                        {phoneVerification.sent && !phoneVerification.verified && (
                          <>
                            <input
                              type="text"
                              maxLength={6}
                              className="border border-indigo-200 rounded px-2 py-1 w-28 text-base"
                              placeholder="رمز التحقق"
                              value={phoneVerification.otp}
                              onChange={(e) =>
                                setPhoneVerification((prev) => ({
                                  ...prev,
                                  otp: e.target.value,
                                }))
                              }
                              dir="ltr"
                            />
                            <Button
                              type="button"
                              variant="primary"
                              disabled={
                                phoneVerification.verifying ||
                                phoneVerification.otp.length !== 6
                              }
                              onClick={async () => {
                                setPhoneVerification((prev) => ({
                                  ...prev,
                                  verifying: true,
                                }));
                                try {
                                  // معالجة الرقم السوري ليكون بنفس صيغة الإرسال
                                  let phoneToSend = formData.phone;
                                  if (
                                    (formData.countryCode === "+963" ||
                                      formData.countryCode === "963") &&
                                    phoneToSend.startsWith("0")
                                  ) {
                                    phoneToSend =
                                      formData.countryCode +
                                      phoneToSend.slice(1);
                                  } else {
                                    phoneToSend =
                                      formData.countryCode + phoneToSend;
                                  }
                                  const response = await authService.verifyOtp(
                                    phoneToSend,
                                    phoneVerification.otp
                                  );
                                  if (response.success) {
                                    setPhoneVerification((prev) => ({
                                      ...prev,
                                      verifying: false,
                                      verified: true,
                                    }));
                                    toastUtils.showToast(
                                      "تم التحقق من رقم الهاتف بنجاح! يمكنك الآن الضغط على زر متابعة.",
                                      "success",
                                      3000
                                    );
                                  } else {
                                    toastUtils.showToast(
                                      response.message || "رمز التحقق غير صحيح",
                                      "error",
                                      2000
                                    );
                                    throw new Error(
                                      response.message || "رمز التحقق غير صحيح"
                                    );
                                  }
                                } catch (error) {
                                  setPhoneVerification((prev) => ({
                                    ...prev,
                                    verifying: false,
                                  }));
                                  toastUtils.showToast(
                                    error.message || "رمز التحقق غير صحيح",
                                    "error",
                                    2000
                                  );
                                  setErrors({
                                    ...errors,
                                    phone:
                                      error.message || "رمز التحقق غير صحيح",
                                  });
                                }
                              }}
                            >
                              {phoneVerification.verifying
                                ? "جاري التحقق..."
                                : "تحقق"}
                            </Button>
                            {phoneVerification.verified && (
                              <span className="text-green-600 ml-2">
                                تم التحقق ✓
                              </span>
                            )}
                          </>
                        )}
                      </div>
                    </div>
                  )}

                  {/* حقل إدخال البريد الإلكتروني */}
                  {registerType === "email" && (
                    <Input
                      label="البريد الإلكتروني"
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="أدخل البريد الإلكتروني"
                      error={errors.email}
                      required
                      className="text-base py-2 border-indigo-200 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 mb-3"
                      dir="ltr"
                    />
                  )}

                  <Input
                    label="كلمة المرور"
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    placeholder="أدخل كلمة المرور"
                    error={errors.password}
                    required
                    className="text-base py-2 border-indigo-200 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 "
                  />

                  <Input
                    label="تأكيد كلمة المرور"
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    placeholder="أعد إدخال كلمة المرور"
                    error={errors.confirmPassword}
                    required
                    className="text-base py-2 border-indigo-200 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                  />

                  <LoadingButton
                    type="submit"
                    variant="primary"
                    fullWidth
                    isLoading={isLoading}
                    loadingText="جاري التحقق..."
                    className="mt-4 bg-gradient-to-r from-blue-500 to-indigo-500 hover:from-blue-600 hover:to-indigo-600 text-white transition-all duration-200 shadow-md hover:shadow-lg text-base py-2 relative overflow-hidden group"
                  >
                    <span className="relative z-10">متابعة</span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </LoadingButton>
                </form>
              ) : (
                <form onSubmit={handleRegister}>
                  {/* عرض رسالة التحقق من البريد الإلكتروني */}
                  <div
                    className={`mb-6 p-4 rounded-lg ${
                      darkMode ? "bg-indigo-900/20" : "bg-indigo-50"
                    } border ${
                      darkMode ? "border-indigo-800" : "border-indigo-200"
                    }`}
                  >
                    <div className="flex items-center mb-3">
                      <div
                        className={`w-10 h-10 rounded-full flex items-center justify-center mr-3 ${
                          emailVerification.verified
                            ? "bg-green-100"
                            : emailVerification.checking
                            ? "bg-yellow-100"
                            : darkMode
                            ? "bg-indigo-800"
                            : "bg-indigo-100"
                        }`}
                      >
                        {emailVerification.verified ? (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-green-600"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        ) : emailVerification.checking ? (
                          <svg
                            className="animate-spin h-5 w-5 text-yellow-600"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                        ) : (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className={`h-5 w-5 ${
                              darkMode ? "text-indigo-300" : "text-indigo-600"
                            }`}
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                            />
                          </svg>
                        )}
                      </div>
                      <div>
                        <h3
                          className={`font-bold ${
                            emailVerification.verified
                              ? "text-green-700"
                              : emailVerification.checking
                              ? "text-yellow-700"
                              : darkMode
                              ? "text-indigo-300"
                              : "text-indigo-700"
                          }`}
                        >
                          {emailVerification.verified
                            ? "تم تأكيد البريد الإلكتروني!"
                            : emailVerification.checking
                            ? "جاري التحقق..."
                            : "تحقق من بريدك الإلكتروني"}
                        </h3>
                        <p
                          className={`text-sm ${
                            darkMode ? "text-gray-400" : "text-gray-600"
                          }`}
                        >
                          {emailVerification.verified
                            ? "يمكنك الآن إكمال التسجيل"
                            : `تم إرسال رابط التحقق إلى ${formData.email}`}
                        </p>
                      </div>
                    </div>

                    {!emailVerification.verified && (
                      <div className="mt-4 p-4 bg-white rounded-lg border border-indigo-100 shadow-sm">
                        <p className="text-gray-700 mb-3">
                          لإكمال عملية التسجيل، يرجى:
                        </p>
                        <ol className="list-decimal list-inside space-y-2 text-gray-600 text-sm">
                          <li>فتح بريدك الإلكتروني</li>
                          <li>البحث عن رسالة من {settings?.siteName || "JobScope"}</li>
                          <li>النقر على رابط التحقق في الرسالة</li>
                          <li className="font-medium text-indigo-600">
                            ستتم إعادة توجيهك تلقائياً إلى هذه الصفحة مع تأكيد
                            الحساب
                          </li>
                        </ol>
                      </div>
                    )}

                    {emailVerification.verified && (
                      <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm">
                        <div className="flex items-center">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 text-green-600 mr-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                            />
                          </svg>
                          <p className="text-green-700 font-medium">
                            تم تأكيد بريدك الإلكتروني بنجاح! يمكنك الآن إكمال
                            التسجيل.
                          </p>
                        </div>
                      </div>
                    )}

                    <div className="flex justify-between items-center mt-4">
                      <button
                        type="button"
                        className={`text-sm ${
                          darkMode ? "text-indigo-400" : "text-indigo-600"
                        } hover:underline flex items-center`}
                        onClick={() => setStep(1)}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 mr-1"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M11 17l-5-5m0 0l5-5m-5 5h12"
                          />
                        </svg>
                        تعديل البيانات
                      </button>

                      <button
                        type="button"
                        className={`text-sm ${
                          darkMode ? "text-indigo-400" : "text-indigo-600"
                        } hover:underline flex items-center ${
                          resendDisabled ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                        onClick={handleResendOtp}
                        disabled={resendDisabled}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 mr-1"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                          />
                        </svg>
                        {resendDisabled
                          ? `إعادة الإرسال (${resendCountdown})`
                          : "إعادة إرسال رابط التحقق"}
                      </button>
                    </div>
                  </div>

                  {/* زر التحقق من التأكيد أو إكمال التسجيل */}
                  <LoadingButton
                    type="submit"
                    variant="primary"
                    fullWidth
                    isLoading={isLoading}
                    loadingText={
                      emailVerification.verified
                        ? "جاري إكمال التسجيل..."
                        : "جاري التحقق..."
                    }
                    className={`mt-4 ${
                      emailVerification.verified
                        ? darkMode
                          ? "bg-gradient-to-r from-green-700 to-green-600 hover:from-green-800 hover:to-green-700"
                          : "bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600"
                        : darkMode
                        ? "bg-gradient-to-r from-indigo-700 to-blue-700 hover:from-indigo-800 hover:to-blue-800"
                        : "bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700"
                    } text-white transition-all duration-200 shadow-md hover:shadow-lg text-base py-2 relative overflow-hidden group`}
                  >
                    <span className="relative z-10">
                      {emailVerification.verified
                        ? "إكمال التسجيل"
                        : "تحقق من التأكيد"}
                    </span>
                    <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                  </LoadingButton>

                  {/* زر الانتقال إلى صفحة تسجيل الدخول */}
                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-600 mb-2">
                      هل قمت بتأكيد بريدك الإلكتروني بالفعل؟
                    </p>
                    <button
                      type="button"
                      className={`text-sm ${
                        darkMode ? "text-indigo-400" : "text-indigo-600"
                      } hover:underline`}
                      onClick={() => navigate("/login")}
                    >
                      الانتقال إلى صفحة تسجيل الدخول
                    </button>
                  </div>
                </form>
              )}

              <div className="mt-6 text-center">
                <p
                  className={`${
                    darkMode ? "text-gray-300" : "text-indigo-600"
                  } text-sm mb-3`}
                >
                  هل تريد التسجيل كحرفي بدلاً من ذلك؟{" "}
                  <Link
                    to="/register/craftsman"
                    className={`${
                      darkMode ? "text-indigo-400" : "text-indigo-600"
                    } font-medium hover:text-indigo-800 transition-colors duration-200 border-b-2 border-indigo-200 hover:border-indigo-600 relative inline-block group`}
                  >
                    <span className="relative z-10">انتقل إلى تسجيل حرفي</span>
                    <span className="absolute bottom-0 left-0 right-0 h-1 bg-indigo-100 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
                  </Link>
                </p>
              </div>

              <div className="mt-4 text-center">
                <Link
                  to="/"
                  className="text-indigo-500 text-sm hover:text-indigo-700 transition-colors duration-200 border-b border-transparent hover:border-indigo-500 inline-block"
                >
                  العودة إلى الصفحة الرئيسية
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default ClientRegisterPage;
