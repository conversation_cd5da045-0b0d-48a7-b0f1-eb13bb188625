import React from "react";
import { motion } from "framer-motion";
import useThemeStore from "../../../store/themeStore";
import {
  Lightbulb,
  Droplets,
  Hammer,
  PaintBucket,
  Sofa,
  Car,
  Scissors,
  Building,
  Tractor,
} from "lucide-react";
import { Link } from "react-router-dom";

// قائمة المهن مع أيقونات Lucide
const professions = [
  {
    name: "كهربائي",
    icon: <Lightbulb size={28} />,
    description: "صيانة وتركيب الأنظمة الكهربائية",
    color: "from-yellow-500 to-amber-600",
  },
  {
    name: "سباك",
    icon: <Droplets size={28} />,
    description: "تركيب وإصلاح أنظمة المياه والصرف الصحي",
    color: "from-blue-500 to-cyan-600",
  },
  {
    name: "نجار",
    icon: <Hammer size={28} />,
    description: "تصنيع وإصلاح المنتجات الخشبية",
    color: "from-amber-600 to-orange-700",
  },
  {
    name: "دهان",
    icon: <PaintBucket size={28} />,
    description: "طلاء وتشطيب الجدران والأسطح",
    color: "from-purple-500 to-indigo-600",
  },
  {
    name: "مصمم ديكور",
    icon: <Sofa size={28} />,
    description: "تصميم وتنفيذ الديكورات الداخلية",
    color: "from-pink-500 to-rose-600",
  },
  {
    name: "ميكانيكي",
    icon: <Car size={28} />,
    description: "إصلاح وصيانة المركبات والآلات",
    color: "from-gray-600 to-gray-800",
  },
  {
    name: "حداد",
    icon: <Scissors size={28} />,
    description: "صناعة البوابات الحديدية وحماية النوافذ",
    color: "from-red-600 to-gray-800",
  },
  {
    name: "بناء",
    icon: <Building size={28} />,
    description: "تشييد وترميم المباني والمنشآت",
    color: "from-stone-600 to-stone-800",
  },
  {
    name: "مزارع",
    icon: <Tractor size={28} />,
    description: "العناية بالنباتات وتنسيق الحدائق",
    color: "from-green-500 to-emerald-600",
  },
];

const ProfessionsSection = () => {
  const darkMode = useThemeStore((state) => state.darkMode);

  // تكوين التأثيرات الحركية
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.4,
      },
    },
  };

  return (
    <section
      id="professions"
      className={`py-20 ${
        darkMode
          ? "bg-gradient-to-b from-gray-900 via-indigo-950/20 to-gray-900"
          : "bg-gradient-to-b from-blue-50 via-indigo-100/30 to-blue-50"
      } transition-colors duration-300 relative overflow-hidden border-t ${
        darkMode ? "border-indigo-900" : "border-indigo-200"
      }`}
      style={{
        boxShadow: darkMode
          ? "0 -8px 20px -5px rgba(0, 0, 0, 0.3)"
          : "0 -8px 20px -5px rgba(79, 70, 229, 0.1)",
      }}
    >
      {/* خط متدرج في الأعلى */}
      <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-indigo-500 to-transparent opacity-30"></div>

      {/* زخارف خلفية */}
      <div className="absolute inset-0 overflow-hidden bg-pattern"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <h2
            className={`text-3xl md:text-5xl font-bold ${
              darkMode ? "text-indigo-300" : "text-indigo-800"
            } relative inline-block transition-colors duration-300 mb-4`}
          >
            <span className="relative z-10">المهن المتوفرة</span>
            <span
              className={`absolute bottom-0 left-0 right-0 h-3 ${
                darkMode ? "bg-indigo-500" : "bg-indigo-300"
              } opacity-40 transform -rotate-1 z-0`}
            ></span>
          </h2>

          <p
            className={`max-w-3xl mx-auto text-lg ${
              darkMode ? "text-gray-300" : "text-gray-700"
            }`}
          >
            اكتشف مجموعة متنوعة من المهن والخدمات المتاحة على منصتنا
          </p>
        </motion.div>

        <motion.div
          className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 md:gap-6"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {professions.map((profession) => (
            <motion.div
              key={profession.name}
              className="feature-card"
              variants={itemVariants}
            >
              <Link
                to={`/search?profession=${profession.name}`}
                className={`flex flex-col items-center p-4 h-full rounded-xl ${
                  darkMode
                    ? "bg-gray-800/50 hover:bg-gray-800/80 border border-gray-700"
                    : "bg-white/80 hover:bg-white border border-indigo-100"
                } shadow-md hover:shadow-lg transition-all duration-300`}
              >
                <div
                  className={`w-16 h-16 rounded-full flex items-center justify-center shadow-lg mb-3 transform transition-all duration-300 ${
                    darkMode
                      ? "bg-gradient-to-br from-indigo-900 to-indigo-800 text-indigo-100"
                      : `bg-gradient-to-br ${profession.color} text-white`
                  }`}
                >
                  {profession.icon}
                </div>
                <span
                  className={`text-center font-bold px-3 py-1.5 rounded-lg mb-2 ${
                    darkMode ? "text-white" : "text-indigo-800"
                  }`}
                >
                  {profession.name}
                </span>
                <p
                  className={`text-xs text-center line-clamp-2 ${
                    darkMode ? "text-gray-400" : "text-gray-600"
                  }`}
                >
                  {profession.description}
                </p>
              </Link>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <Link
            to="/search"
            className={`inline-flex items-center gap-2 px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
              darkMode
                ? "bg-indigo-700 text-white hover:bg-indigo-600"
                : "bg-indigo-600 text-white hover:bg-indigo-700"
            } shadow-md hover:shadow-lg`}
          >
            <span>استكشاف جميع المهن</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </Link>
        </motion.div>
      </div>
    </section>
  );
};

export default ProfessionsSection;
