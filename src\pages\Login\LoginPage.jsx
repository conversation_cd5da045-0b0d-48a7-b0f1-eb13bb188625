import React, { useState, useEffect, useRef } from "react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { motion } from "framer-motion";
import { GoogleLogin } from "@react-oauth/google";
import { jwtDecode } from "jwt-decode";
import Layout from "../../components/layout/Layout";
import Input from "../../components/common/Input";
import Button from "../../components/common/Button";
import LoadingButton from "../../components/common/LoadingButton";

import useUserStore from "../../store/userStore";
import useThemeStore from "../../store/themeStore";
import useSiteSettingsStore from "../../store/siteSettingsStore";
import notificationService from "../../services/notificationService";
import authService from "../../services/authService";
// تم استبدال Firebase بـ Supabase
import supabaseAuthService from "../../services/supabaseAuthService";
import { showToast } from "../../utils/toastUtils";

const LoginPage = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const login = useUserStore((state) => state.login);
  const darkMode = useThemeStore((state) => state.darkMode);
  const { settings, fetchSettings } = useSiteSettingsStore();
  const savedCredentials = useUserStore((state) => state.savedCredentials);
  const rememberMe = useUserStore((state) => state.rememberMe);
  const setRememberMe = useUserStore((state) => state.setRememberMe);
  const setSavedCredentials = useUserStore(
    (state) => state.setSavedCredentials
  );

  // استخدام معلمة الاستعلام لتحديد نوع الحساب
  const queryParams = new URLSearchParams(location.search);
  const accountTypeFromQuery = queryParams.get("type");

  const [accountType, setAccountType] = useState(
    accountTypeFromQuery || savedCredentials?.accountType || "client"
  );
  const [loginType, setLoginType] = useState("email"); // نوع تسجيل الدخول: email أو phone
  const [formData, setFormData] = useState({
    identifier: savedCredentials?.email || "", // البريد الإلكتروني أو رقم الهاتف
    password: savedCredentials?.password || "", // كلمة المرور
  });
  const [errors, setErrors] = useState({});
  const [rememberMeChecked, setRememberMeChecked] = useState(rememberMe);
  const [isLoading, setIsLoading] = useState(false);
  const [isResetLoading, setIsResetLoading] = useState(false);

  // جلب إعدادات الموقع (مع معالجة الأخطاء)
  useEffect(() => {
    const loadSettings = async () => {
      try {
        await fetchSettings();
      } catch (error) {
        console.log("تعذر تحميل إعدادات الموقع في صفحة تسجيل الدخول:", error.message);
        // لا نعرض خطأ للمستخدم لأن هذا ليس مهماً في صفحة تسجيل الدخول
      }
    };

    loadSettings();
  }, [fetchSettings]);

  // التحقق من وجود جلسة سابقة
  useEffect(() => {
    const checkExistingSession = () => {
      const token = localStorage.getItem("token");
      const userData = localStorage.getItem("user");
      const tokenExpiry = localStorage.getItem("tokenExpiry");

      // التحقق من صحة الجلسة
      if (token && userData) {
        // التحقق من انتهاء صلاحية التوكن إذا كان موجود<|im_start|>
        if (tokenExpiry) {
          const expiryDate = new Date(tokenExpiry);
          const now = new Date();

          if (now > expiryDate) {
            // انتهت الصلاحية، مسح البيانات
            localStorage.removeItem("token");
            localStorage.removeItem("user");
            localStorage.removeItem("tokenExpiry");
            return;
          }
        }

        try {
          const user = JSON.parse(userData);
          // الجلسة صالحة، انتقل للصفحة المناسبة
          navigate(user.userType === "craftsman" ? "/profile/my" : "/home");
          return;
        } catch (error) {
          console.error("خطأ في تحليل بيانات المستخدم:", error);
          // مسح البيانات التالفة
          localStorage.removeItem("token");
          localStorage.removeItem("user");
          localStorage.removeItem("tokenExpiry");
        }
      }
    };

    checkExistingSession();
  }, [navigate]);

  // التحقق من وجود رسالة من صفحة الإعدادات
  useEffect(() => {
    if (location.state?.message) {
      // استخدام Toast بدلاً من عرض الرسالة في الصفحة
      showToast(location.state.message, "success", 3000);
      // مسح الرسالة من التاريخ
      navigate(location.pathname, { replace: true });
    }
  }, [location.state, navigate, location.pathname]);

  // مرجع لعنصر reCAPTCHA
  const recaptchaContainerRef = useRef(null);

  // إذا كانت هناك بيانات محفوظة، نقوم بتحديد نوع المعرف (هاتف أو بريد)
  useEffect(() => {
    if (savedCredentials?.email) {
      // تحديد ما إذا كان المعرف بريدًا إلكترونيًا أو رقم هاتف
      if (savedCredentials.email.includes("@")) {
        setLoginType("email");
        setFormData((prev) => ({
          ...prev,
          identifier: savedCredentials.email,
        }));
      } else {
        setLoginType("phone");
        setFormData((prev) => ({
          ...prev,
          identifier: savedCredentials.email,
        }));
      }
    }
  }, [savedCredentials]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });

    // مسح الأخطاء عند كتابة المستخدم
    if (errors[name]) {
      // إذا كان الحقل هو المعرف، تأكد من مسح أي أخطاء متعلقة بنوع المعرف
      if (name === "identifier") {
        setErrors({
          ...errors,
          identifier: "",
          general: "",
        });
      } else {
        setErrors({
          ...errors,
          [name]: "",
        });
      }
    }
  };

  const validateLoginForm = () => {
    let isValid = true;
    const newErrors = {};

    // التحقق من المعرف (البريد الإلكتروني أو رقم الهاتف)
    if (!formData.identifier) {
      newErrors.identifier =
        loginType === "email" ? "البريد الإلكتروني مطلوب" : "رقم الهاتف مطلوب";
      isValid = false;
    } else if (loginType === "email") {
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.identifier)) {
        newErrors.identifier = "يرجى إدخال بريد إلكتروني صالح";
        isValid = false;
      }
    } else if (loginType === "phone") {
      // التحقق من صحة رقم الهاتف (يجب أن يحتوي على أرقام فقط ويكون بين 9-15 رقم)
      const phoneRegex = /^[+]?[0-9]{9,15}$/;
      if (!phoneRegex.test(formData.identifier.replace(/\s/g, ""))) {
        newErrors.identifier = "يرجى إدخال رقم هاتف صالح";
        isValid = false;
      }
    }

    // التحقق من كلمة المرور
    if (!formData.password) {
      newErrors.password = "كلمة المرور مطلوبة";
      isValid = false;
    } else if (formData.password.length < 6) {
      newErrors.password = "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل";
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  const handleLogin = async (e) => {
    e.preventDefault();

    if (validateLoginForm()) {
      setIsLoading(true);
      try {
        // التحقق من وجود المعرف (البريد الإلكتروني أو رقم الهاتف)
        let checkResult;
        if (loginType === "email") {
          checkResult = await authService.checkEmailExists(formData.identifier);
        } else {
          checkResult = await authService.checkPhoneExists(formData.identifier);
        }
        console.log("Identifier check result:", checkResult);

        // محاولة تسجيل الدخول باستخدام API أولاً ثم Firebase إذا فشل
        let loginResult;
        try {
          // محاولة تسجيل الدخول باستخدام API
          loginResult = await authService.login({
            email: formData.identifier, // سيتم استخدام هذا الحقل للبريد أو الهاتف
            password: formData.password,
            userType: accountType,
            rememberMe: rememberMeChecked,
          });

          // إذا نجح تسجيل الدخول باستخدام API، نستخدم النتيجة مباشرة
          console.log("API login successful:", loginResult);
        } catch (loginError) {
          // إذا فشل تسجيل الدخول باستخدام API، نحدد نوع الخطأ بدقة
          console.log("API login failed:", loginError);

          // تحديد رسالة الخطأ بناءً على حالة المعرف
          if (!checkResult.exists) {
            // المعرف غير موجود في قاعدة البيانات
            const identifierType =
              loginType === "email" ? "البريد الإلكتروني" : "رقم الهاتف";
            throw new Error(
              `${identifierType} غير مسجل في النظام. يرجى التحقق من ${identifierType} أو إنشاء حساب جديد.`
            );
          }

          // المعرف موجود لكن فشل تسجيل الدخول، نحاول Supabase (فقط للبريد الإلكتروني)
          if (loginType === "email") {
            console.log(
              "Email exists but API login failed, trying Supabase authentication"
            );
            console.log("Attempting Supabase login with:", {
              email: formData.identifier,
              passwordLength: formData.password.length,
            });

            const supabaseResult = await supabaseAuthService.loginWithEmailAndPassword(
              formData.identifier,
              formData.password
            );

            console.log("Supabase login result:", supabaseResult);

            if (supabaseResult.success) {
              console.log("Supabase login successful:", supabaseResult);

              // المستخدم موجود في Supabase ولكن غير موجود في قاعدة البيانات
              // نقوم بتسجيله في قاعدة البيانات
              const userData = {
                uid: supabaseResult.user.uid,
                name:
                  supabaseResult.user.name || formData.identifier.split("@")[0],
                email: formData.identifier,
                phone: "",
                userType: accountType,
                emailVerified: supabaseResult.user.emailVerified,
                isSupabase: true, // تحديد أن هذا مستخدم Supabase
              };

              console.log("Registering Supabase user with backend:", userData);

              try {
                // تسجيل المستخدم في قاعدة البيانات باستخدام النقطة النهائية القديمة مباشرة
                loginResult = await authService.registerFirebaseUser(userData);
                console.log("Registration result:", loginResult);
              } catch (registerError) {
                console.error(
                  "Error registering Supabase user:",
                  registerError
                );

                // إذا فشل التسجيل، نستخدم بيانات Supabase مباشرة
                loginResult = {
                  user: {
                    ...userData,
                    _id: userData.uid,
                    id: userData.uid,
                  },
                  token:
                    "supabase-" +
                    Math.random()
                      .toString(36)
                      .substring(2),
                };

                // تخزين التوكن
                localStorage.setItem("token", loginResult.token);
                localStorage.setItem("user", JSON.stringify(loginResult.user));
              }
            } else {
              // إذا فشل تسجيل الدخول في Supabase، نحدد نوع الخطأ
              let errorMessage = "حدث خطأ أثناء تسجيل الدخول";

              if (supabaseResult.error) {
                const errorCode =
                  supabaseResult.error.message || supabaseResult.error.code;

                console.log("Detailed Supabase error analysis:", {
                  errorCode,
                  originalError: supabaseResult.error,
                  identifierExists: checkResult.exists,
                });

                if (
                  errorCode.includes("Invalid login credentials") ||
                  errorCode.includes("invalid_credentials") ||
                  errorCode.includes("Invalid email or password")
                ) {
                  // نحن نعرف بالفعل أن المعرف موجود من التحقق السابق
                  // لذا المشكلة في كلمة المرور
                  if (checkResult.exists) {
                    errorMessage = "كلمة المرور غير صحيحة";
                  } else {
                    errorMessage =
                      loginType === "email"
                        ? "البريد الإلكتروني غير مسجل"
                        : "رقم الهاتف غير مسجل";
                  }
                } else if (
                  errorCode.includes("Email not confirmed") ||
                  errorCode.includes("email_not_confirmed")
                ) {
                  errorMessage = "يرجى تأكيد بريدك الإلكتروني أولاً";
                } else if (
                  errorCode.includes("Too many requests") ||
                  errorCode.includes("rate_limit")
                ) {
                  errorMessage = "محاولات كثيرة، انتظر قليلاً";
                } else if (
                  errorCode.includes("Network") ||
                  errorCode.includes("network") ||
                  errorCode.includes("connection")
                ) {
                  errorMessage = "مشكلة في الاتصال بالإنترنت";
                } else {
                  // للأخطاء غير المعروفة، نعرض رسالة عامة مع تفاصيل للمطور
                  console.error("Unknown Supabase error:", {
                    errorCode,
                    fullError: supabaseResult.error,
                    identifierExists: checkResult.exists,
                  });
                  // استخدام الرسالة من Supabase مباشرة إذا كانت قصيرة، وإلا رسالة عامة
                  if (errorCode.length < 50) {
                    errorMessage = errorCode;
                  } else {
                    errorMessage = "حدث خطأ غير متوقع";
                  }
                }
              } else {
                // إذا لم يكن هناك تفاصيل خطأ من Supabase
                console.error(
                  "Supabase failed without error details:",
                  supabaseResult
                );
                errorMessage = "حدث خطأ في الخادم";
              }

              throw new Error(errorMessage);
            }
          } else {
            // إذا كان نوع تسجيل الدخول برقم الهاتف وفشل API
            // نعرض رسالة خطأ مناسبة لأن Supabase لا يدعم رقم الهاتف
            throw new Error("كلمة المرور غير صحيحة");
          }
        }

        // إعداد بيانات المستخدم من الاستجابة
        const userData = loginResult.user || {
          uid: loginResult.uid || "temp-uid",
          _id:
            loginResult._id ||
            loginResult.id ||
            loginResult.uid ||
            Date.now().toString(),
          id:
            loginResult._id ||
            loginResult.id ||
            loginResult.uid ||
            Date.now().toString(),
          name: loginResult.name || "مستخدم",
          email:
            loginType === "email"
              ? formData.identifier
              : loginResult.email || "",
          phone:
            loginType === "phone"
              ? formData.identifier
              : loginResult.phone || "",
          userType: accountType,
          emailVerified: true,
        };

        // طباعة معلومات المستخدم للتصحيح
        console.log("معلومات المستخدم بعد تسجيل الدخول:", {
          _id: userData._id,
          id: userData.id,
          name: userData.name,
          userType: accountType,
        });

        console.log("User data:", userData);

        // حفظ بيانات الاعتماد إذا تم تحديد خيار "تذكرني"
        if (rememberMeChecked) {
          const credentials = {
            email: formData.identifier, // حفظ المعرف (بريد أو هاتف)
            password: formData.password,
            accountType: accountType,
          };
          setSavedCredentials(credentials);
        } else {
          // إذا لم يتم تحديد خيار "تذكرني"، نقوم بمسح البيانات المحفوظة
          setSavedCredentials(null);
        }

        // تحديث خيار "تذكرني" في المخزن
        setRememberMe(rememberMeChecked);

        // استخدام نوع المستخدم الفعلي من قاعدة البيانات بدلاً من الاختيار
        const actualUserType = userData.userType || accountType;
        console.log(
          "نوع المستخدم الفعلي:",
          actualUserType,
          "نوع الحساب المختار:",
          accountType
        );

        // تسجيل دخول المستخدم في المخزن المحلي
        login(
          userData,
          actualUserType,
          rememberMeChecked,
          rememberMeChecked
            ? {
                email: formData.identifier, // حفظ المعرف (بريد أو هاتف)
                password: formData.password,
                accountType: actualUserType,
              }
            : null
        );

        // إضافة إشعار ترحيبي فقط (مع معالجة الأخطاء)
        try {
          notificationService.createSystemNotification(
            `مرحباً بك في ${settings?.siteName || "JobScope"}`,
            `مرحباً ${userData.name}، شكراً لتسجيل دخولك في منصتنا.`
          );
        } catch (notificationError) {
          console.log("تعذر إنشاء إشعار الترحيب:", notificationError.message);
          // لا نعرض خطأ للمستخدم لأن هذا ليس مهماً
        }

        // الانتقال إلى الصفحة المناسبة بناءً على نوع المستخدم الفعلي
        navigate(actualUserType === "craftsman" ? "/profile/my" : "/home");
      } catch (error) {
        console.error("Login error:", error);
        showToast(error.message || "حدث خطأ أثناء تسجيل الدخول", "error", 3000);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleGoogleLogin = async (credentialResponse) => {
    console.log("Google OAuth response:", credentialResponse);
    setIsLoading(true);

    try {
      // فك تشفير الرمز المميز JWT للحصول على معلومات المستخدم
      const decodedToken = jwtDecode(credentialResponse.credential);
      console.log("Decoded Google token:", decodedToken);

      // استخدام البيانات من JWT مباشرة بدلاً من استدعاء Firebase
      // هذا حل مؤقت حتى يتم إصلاح مشكلة الأصل في Google Cloud Console
      const userData = {
        uid: decodedToken.sub,
        name: decodedToken.name,
        email: decodedToken.email,
        phone: "",
        userType: accountType,
        googleId: decodedToken.sub,
        image: decodedToken.picture || "/img/user-avatar.svg?url",
        emailVerified: decodedToken.email_verified,
      };

      // تخطي استدعاء Firebase مؤقتًا
      /*
      const response = await firebaseAuthService.loginWithGoogle();

      if (!response.success) {
        throw new Error(response.error.message);
      }
      */

      // استخدام البيانات من JWT لتسجيل المستخدم في قاعدة البيانات
      console.log("Registering Google user with API:", userData);

      // التأكد من وجود معرف للمستخدم
      if (!userData._id && !userData.id) {
        userData._id = userData.uid || Date.now().toString();
        userData.id = userData._id;
      }

      // طباعة معلومات المستخدم للتصحيح
      console.log("معلومات المستخدم من Google:", {
        _id: userData._id,
        id: userData.id,
        uid: userData.uid,
        name: userData.name,
      });

      // حفظ بيانات المستخدم في قاعدة البيانات
      let registerResponse;
      try {
        // إضافة محاولات إعادة المحاولة في حالة فشل الاتصال
        let attempts = 0;
        const maxAttempts = 3;

        while (attempts < maxAttempts) {
          try {
            registerResponse = await authService.registerFirebaseUser(userData);
            console.log("Registration response:", registerResponse);

            // التأكد من وجود معرف في الاستجابة
            if (registerResponse && registerResponse.user) {
              if (!registerResponse.user._id && !registerResponse.user.id) {
                registerResponse.user._id = userData._id;
                registerResponse.user.id = userData.id;
              }
            }

            break; // الخروج من الحلقة في حالة النجاح
          } catch (err) {
            attempts++;
            console.error(
              `Error registering user with API (attempt ${attempts}/${maxAttempts}):`,
              err
            );

            if (attempts >= maxAttempts) {
              throw err; // إعادة رمي الخطأ بعد استنفاد جميع المحاولات
            }

            // انتظار قبل إعادة المحاولة
            await new Promise((resolve) => setTimeout(resolve, 1000));
          }
        }
      } catch (error) {
        console.error("All attempts to register user with API failed:", error);
        // في حالة فشل التسجيل في قاعدة البيانات، استخدم البيانات مباشرة
        registerResponse = { user: userData };
      }

      // تسجيل دخول المستخدم في المخزن المحلي
      const finalUserData = registerResponse.user || userData;

      // استخدام نوع المستخدم الفعلي من قاعدة البيانات بدلاً من الاختيار
      const actualUserType = finalUserData.userType || accountType;

      // طباعة معلومات المستخدم النهائية
      console.log("معلومات المستخدم النهائية قبل تسجيل الدخول:", {
        _id: finalUserData._id,
        id: finalUserData.id,
        name: finalUserData.name,
        userType: actualUserType,
        selectedAccountType: accountType,
      });

      login(finalUserData, actualUserType);

      // إضافة إشعار ترحيبي فقط (مع معالجة الأخطاء)
      try {
        notificationService.createSystemNotification(
          `مرحباً بك في ${settings?.siteName || "JobScope"}`,
          `مرحباً ${userData.name}، شكراً لتسجيلك في منصتنا.`
        );
      } catch (notificationError) {
        console.log("تعذر إنشاء إشعار الترحيب:", notificationError.message);
        // لا نعرض خطأ للمستخدم لأن هذا ليس مهماً
      }

      // الانتقال إلى الصفحة المناسبة بناءً على نوع المستخدم الفعلي
      navigate(actualUserType === "craftsman" ? "/profile/my" : "/home");
    } catch (error) {
      console.error("Error with Google login:", error);
      showToast(
        error.message || "حدث خطأ أثناء تسجيل الدخول باستخدام Google",
        "error",
        3000
      );
      handleGoogleLoginError();
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleLoginError = (error) => {
    console.error("Google login failed:", error);

    // إضافة رسالة خطأ أكثر تفصيلاً للمستخدم
    showToast(
      "تعذر تسجيل الدخول باستخدام Google. قد يكون ذلك بسبب مشكلة في الاتصال أو لأن خدمة Google محظورة في موقعك. يرجى استخدام طريقة تسجيل الدخول العادية.",
      "error",
      3000
    );

    // إضافة معلومات تصحيح إضافية في وحدة التحكم
    if (error) {
      console.error("Google login error details:", {
        error,
        message: error.message || "No error message",
        type: error.type || "Unknown error type",
      });
    }

    // إضافة تتبع للأخطاء المتكررة
    if (!window.googleLoginErrors) {
      window.googleLoginErrors = [];
    }
    window.googleLoginErrors.push({
      timestamp: new Date().toISOString(),
      error: error || "No error object provided",
    });

    if (window.googleLoginErrors.length > 5) {
      console.warn(
        "Multiple Google login errors detected:",
        window.googleLoginErrors.length
      );
    }
  };

  return (
    <Layout hideFooter>
      <div
        className={`min-h-screen py-8 md:py-12 ${
          darkMode
            ? "bg-gray-900"
            : "bg-gradient-to-br from-blue-50 to-indigo-100"
        } transition-colors duration-300`}
      >
        <style
          dangerouslySetInnerHTML={{
            __html: `
          @keyframes pulse {
            0% {
              box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.4);
            }
            70% {
              box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
            }
            100% {
              box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
          }
        `,
          }}
        />
        <div className="container mx-auto px-4 pb-4">
          <motion.div
            className={`max-w-lg mx-auto rounded-lg shadow-xl overflow-hidden border-2 ${
              darkMode
                ? "bg-gray-800 border-gray-700"
                : "bg-white border-indigo-200"
            } transition-colors duration-300`}
            initial={{ opacity: 0, y: 20, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
          >
            <div className="p-4 sm:p-6">
              <h2
                className={`text-2xl font-bold text-center mb-4 ${
                  darkMode ? "text-indigo-300" : "text-indigo-800"
                } relative transition-colors duration-300`}
              >
                <span className="relative z-10">تسجيل الدخول</span>
                <span
                  className={`absolute bottom-0 left-0 right-0 h-3 ${
                    darkMode ? "bg-indigo-500" : "bg-indigo-300"
                  } opacity-40 transform -rotate-1 z-0`}
                ></span>
              </h2>

              {/* Account Type Selection */}
              <div className="mb-6">
                <label className="block text-indigo-700 font-medium mb-2">
                  نوع الحساب
                </label>
                <div className="grid grid-cols-2 gap-4">
                  <button
                    type="button"
                    className={`p-2 rounded-md border transition-all duration-300 shadow-sm relative overflow-hidden group ${
                      accountType === "craftsman"
                        ? "border-indigo-500 bg-gradient-to-r from-blue-50 to-indigo-100 text-indigo-700 shadow-md"
                        : "border-indigo-200 text-indigo-600 hover:bg-indigo-50"
                    }`}
                    onClick={() => setAccountType("craftsman")}
                  >
                    <span className="relative z-10 flex items-center justify-center gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                      حرفي
                    </span>
                    <span
                      className={`absolute inset-0 bg-indigo-50 opacity-0 transform -skew-x-12 -translate-x-full transition-all duration-500 ${
                        accountType !== "craftsman"
                          ? "group-hover:translate-x-full group-hover:opacity-50"
                          : ""
                      }`}
                    ></span>
                  </button>
                  <button
                    type="button"
                    className={`p-2 rounded-md border transition-all duration-300 shadow-sm relative overflow-hidden group ${
                      accountType === "client"
                        ? "border-indigo-500 bg-gradient-to-r from-blue-50 to-indigo-100 text-indigo-700 shadow-md"
                        : "border-indigo-200 text-indigo-600 hover:bg-indigo-50"
                    }`}
                    onClick={() => setAccountType("client")}
                  >
                    <span className="relative z-10 flex items-center justify-center gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                      طالب خدمة
                    </span>
                    <span
                      className={`absolute inset-0 bg-indigo-50 opacity-0 transform -skew-x-12 -translate-x-full transition-all duration-500 ${
                        accountType !== "client"
                          ? "group-hover:translate-x-full group-hover:opacity-50"
                          : ""
                      }`}
                    ></span>
                  </button>
                </div>
              </div>

              {/* عنصر reCAPTCHA غير مرئي */}
              <div id="recaptcha-container" ref={recaptchaContainerRef}></div>

              {/* نموذج تسجيل الدخول */}
              <form onSubmit={handleLogin}>
                {/* أزرار اختيار نوع تسجيل الدخول */}
                <div className="mb-4">
                  <label
                    className={`block font-medium mb-2 ${
                      darkMode ? "text-gray-300" : "text-indigo-700"
                    }`}
                  >
                    طريقة تسجيل الدخول
                  </label>
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      type="button"
                      className={`p-3 rounded-md border transition-all duration-300 shadow-sm relative overflow-hidden group ${
                        loginType === "email"
                          ? "border-indigo-500 bg-gradient-to-r from-blue-50 to-indigo-100 text-indigo-700 shadow-md"
                          : "border-indigo-200 text-indigo-600 hover:bg-indigo-50"
                      }`}
                      onClick={() => {
                        setLoginType("email");
                        setFormData((prev) => ({ ...prev, identifier: "" }));
                        setErrors({});
                      }}
                    >
                      <span className="relative z-10 flex items-center justify-center gap-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                        البريد الإلكتروني
                      </span>
                    </button>
                    <button
                      type="button"
                      className={`p-3 rounded-md border transition-all duration-300 shadow-sm relative overflow-hidden group ${
                        loginType === "phone"
                          ? "border-indigo-500 bg-gradient-to-r from-blue-50 to-indigo-100 text-indigo-700 shadow-md"
                          : "border-indigo-200 text-indigo-600 hover:bg-indigo-50"
                      }`}
                      onClick={() => {
                        setLoginType("phone");
                        setFormData((prev) => ({ ...prev, identifier: "" }));
                        setErrors({});
                      }}
                    >
                      <span className="relative z-10 flex items-center justify-center gap-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                          />
                        </svg>
                        رقم الهاتف
                      </span>
                    </button>
                  </div>
                </div>

                {/* حقل المعرف (البريد الإلكتروني أو رقم الهاتف) */}
                <Input
                  label={
                    loginType === "email" ? "البريد الإلكتروني" : "رقم الهاتف"
                  }
                  type={loginType === "email" ? "email" : "tel"}
                  name="identifier"
                  value={formData.identifier}
                  onChange={handleInputChange}
                  placeholder={
                    loginType === "email"
                      ? "أدخل البريد الإلكتروني"
                      : "أدخل رقم الهاتف"
                  }
                  error={errors.identifier}
                  required
                  className="text-base py-2 border-indigo-200 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50 mb-3"
                  dir="ltr"
                />

                <Input
                  label="كلمة المرور"
                  type="password"
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="أدخل كلمة المرور"
                  error={errors.password}
                  required
                  className="text-base py-2 border-indigo-200 focus:border-indigo-500 focus:ring focus:ring-indigo-200 focus:ring-opacity-50"
                />

                {/* زر نسيت كلمة المرور - يظهر فقط للبريد الإلكتروني */}
                {loginType === "email" && (
                  <div className="flex justify-start mt-1 mb-3">
                    <button
                      type="button"
                      onClick={async () => {
                        try {
                          if (!formData.identifier) {
                            showToast(
                              "يرجى إدخال البريد الإلكتروني أولاً",
                              "warning",
                              3000
                            );
                            return;
                          }

                          // استخدام متغير منفصل لتتبع حالة تحميل إعادة تعيين كلمة المرور
                          setIsResetLoading(true);

                          // إرسال بريد إلكتروني لإعادة تعيين كلمة المرور باستخدام Supabase
                          const result = await supabaseAuthService.sendPasswordResetEmail(
                            formData.identifier
                          );

                          setIsResetLoading(false);

                          if (result.success) {
                            // إظهار رسالة نجاح
                            showToast(
                              "تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني",
                              "success",
                              3000
                            );
                          } else {
                            // إظهار رسالة خطأ
                            showToast(
                              `حدث خطأ: ${result.error?.message ||
                                "خطأ غير معروف"}`,
                              "error",
                              3000
                            );
                          }
                        } catch (error) {
                          console.error(
                            "Error sending password reset email:",
                            error
                          );
                          setIsResetLoading(false);
                          showToast(
                            "حدث خطأ أثناء محاولة إرسال بريد إعادة تعيين كلمة المرور",
                            "error",
                            3000
                          );
                        }
                      }}
                      className={`text-sm ${
                        darkMode
                          ? "text-indigo-400 hover:text-indigo-300"
                          : "text-indigo-600 hover:text-indigo-700"
                      } transition-colors duration-200 font-medium hover:underline flex items-center gap-1`}
                      disabled={isResetLoading}
                    >
                      {isResetLoading ? (
                        <>
                          <svg
                            className="animate-spin -ml-1 mr-2 h-4 w-4"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                          >
                            <circle
                              className="opacity-25"
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="4"
                            ></circle>
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                            ></path>
                          </svg>
                          جاري الإرسال...
                        </>
                      ) : (
                        <>
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-4 w-4"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                            />
                          </svg>
                          نسيت كلمة المرور؟
                        </>
                      )}
                    </button>
                  </div>
                )}

                <div className="mt-4 flex items-center">
                  <input
                    type="checkbox"
                    id="rememberMe"
                    checked={rememberMeChecked}
                    onChange={(e) => setRememberMeChecked(e.target.checked)}
                    className="ml-2 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  />
                  <label
                    htmlFor="rememberMe"
                    className={`${
                      darkMode ? "text-gray-300" : "text-indigo-700"
                    } text-sm font-medium`}
                  >
                    تذكرني
                  </label>
                </div>

                <LoadingButton
                  type="submit"
                  variant="primary"
                  fullWidth
                  isLoading={isLoading && !isResetLoading}
                  loadingText="جاري تسجيل الدخول..."
                  className={`mt-4 ${
                    darkMode
                      ? "bg-gradient-to-r from-[#3730A3] to-[#4238C8] hover:from-[#322e92] hover:to-[#3b32b4]"
                      : "bg-gradient-to-r from-[#4238C8] to-[#3730A3] hover:from-[#3b32b4] hover:to-[#322e92]"
                  } text-white transition-all duration-200 shadow-md hover:shadow-lg text-base py-2 relative overflow-hidden group`}
                  style={{
                    animation: isLoading ? "none" : "pulse 2s infinite",
                  }}
                >
                  <span className="relative z-10">تسجيل الدخول</span>
                  <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                </LoadingButton>
              </form>

              {/* فاصل أفقي مع نص "أو" */}
              <div className="mt-8 mb-6 relative flex items-center">
                <div className="flex-grow border-t border-gray-300"></div>
                <span
                  className={`flex items-center justify-center w-10 h-10 rounded-full ${
                    darkMode
                      ? "bg-gray-700 text-indigo-300"
                      : "bg-indigo-100 text-indigo-600"
                  } mx-4 text-sm font-medium`}
                >
                  أو
                </span>
                <div className="flex-grow border-t border-gray-300"></div>
              </div>

              {/* تسجيل الدخول باستخدام Google */}
              <div className="mb-6">
                <div className="flex flex-col items-center w-full">
                  {/* إضافة معالجة أخطاء أفضل لمكون GoogleLogin */}
                  <div className="relative w-full flex justify-center">
                    {/* إضافة محاولة/استثناء حول مكون GoogleLogin */}
                    {(() => {
                      try {
                        return (
                          <GoogleLogin
                            onSuccess={handleGoogleLogin}
                            onError={handleGoogleLoginError}
                            text="signin_with"
                            shape="pill"
                            locale="ar"
                            theme={darkMode ? "filled_black" : "outline"}
                            logo_alignment="center"
                            width="300"
                            useOneTap={false}
                            auto_select={false}
                            cancel_on_tap_outside={true}
                          />
                        );
                      } catch (error) {
                        console.error(
                          "Error rendering GoogleLogin component:",
                          error
                        );
                        return (
                          <button
                            onClick={() => {
                              showToast(
                                "تعذر تحميل مكون تسجيل الدخول باستخدام Google. يرجى استخدام طريقة تسجيل الدخول العادية.",
                                "error",
                                3000
                              );
                            }}
                            className={`flex items-center justify-center gap-2 py-2 px-4 rounded-full border ${
                              darkMode
                                ? "bg-gray-800 text-white border-gray-700"
                                : "bg-white text-gray-700 border-gray-300"
                            } shadow-md hover:shadow-lg transition-all duration-300`}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                            >
                              <path
                                fill="#4285F4"
                                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                              />
                              <path
                                fill="#34A853"
                                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                              />
                              <path
                                fill="#FBBC05"
                                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                              />
                              <path
                                fill="#EA4335"
                                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                              />
                              <path fill="none" d="M1 1h22v22H1z" />
                            </svg>
                            <span>تسجيل الدخول باستخدام Google</span>
                          </button>
                        );
                      }
                    })()}
                  </div>
                </div>
              </div>

              {/* قسم إنشاء حساب جديد */}
              <div className="mt-8 mb-6">
                <div
                  className={`p-4 rounded-lg ${
                    darkMode ? "bg-gray-700/50" : "bg-indigo-50"
                  } border ${
                    darkMode ? "border-gray-600" : "border-indigo-200"
                  }`}
                >
                  <h3
                    className={`text-lg font-bold mb-3 text-center ${
                      darkMode ? "text-indigo-300" : "text-indigo-700"
                    }`}
                  >
                    ليس لديك حساب؟
                  </h3>
                  <p
                    className={`text-sm mb-4 text-center ${
                      darkMode ? "text-gray-300" : "text-gray-600"
                    }`}
                  >
                    اختر نوع الحساب الذي تريد إنشاؤه:
                  </p>

                  <div className="grid grid-cols-2 gap-3">
                    <Link to="/register/client" className="w-full">
                      <Button
                        variant="primary"
                        className={`${
                          darkMode
                            ? "bg-gradient-to-r from-indigo-600 to-blue-700 hover:from-indigo-700 hover:to-blue-800"
                            : "bg-gradient-to-r from-indigo-500 to-blue-600 hover:from-indigo-600 hover:to-blue-700"
                        } text-white transition-all duration-200 shadow-md hover:shadow-lg relative overflow-hidden group py-2 px-4 w-full`}
                      >
                        <span className="relative z-10 font-medium flex items-center justify-center gap-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                            />
                          </svg>
                          طالب خدمة
                        </span>
                        <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                      </Button>
                    </Link>

                    <Link to="/register/craftsman" className="w-full">
                      <Button
                        variant="primary"
                        className={`${
                          darkMode
                            ? "bg-gradient-to-r from-purple-600 to-indigo-700 hover:from-purple-700 hover:to-indigo-800"
                            : "bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700"
                        } text-white transition-all duration-200 shadow-md hover:shadow-lg relative overflow-hidden group py-2 px-4 w-full`}
                      >
                        <span className="relative z-10 font-medium flex items-center justify-center gap-2">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                            />
                          </svg>
                          حرفي
                        </span>
                        <span className="absolute inset-0 bg-white opacity-20 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700"></span>
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              {/* رابط العودة للصفحة الرئيسية */}
              <div className="mt-4 text-center">
                <Link
                  to="/"
                  className={`inline-flex items-center gap-1 text-indigo-500 hover:text-indigo-700 transition-colors duration-200 ${
                    darkMode ? "hover:bg-gray-700/50" : "hover:bg-indigo-50"
                  } px-3 py-1 rounded-md`}
                >
                  <span>العودة إلى الصفحة الرئيسية</span>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 rtl:rotate-180"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                </Link>
              </div>

              {/* إضافة مساحة إضافية في الأسفل للتأكد من ظهور جميع المحتويات */}
              <div className="h-2"></div>
            </div>
          </motion.div>
        </div>
      </div>
    </Layout>
  );
};

export default LoginPage;
